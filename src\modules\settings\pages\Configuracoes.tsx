import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { useQuery } from "@tanstack/react-query";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { useEffect, useState } from "react";
import { Youtube, Instagram, Brain, Bot, Sparkles, Cpu } from "lucide-react";
import { LLMConfig, LLMProvider } from "@/types/llm";
import { LLMModel, fetchModelsByProvider } from "@/lib/llm-models";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { TemplateManager } from "../components/TemplateManager";

interface SystemSettings {
  youtubeChannelId?: string;
  youtubeApiKey?: string;
  instagramAccessToken?: string;
  youtubeMonitoring?: {
    scheduleEnabled: boolean;
    respectPlayerState: boolean;
    monitoringEnabled: boolean;
    commentProcessingEnabled: boolean;
    llmEnabled: boolean;
    notificationsEnabled: boolean;
    checkIntervalSeconds: number;
  };
  llm?: LLMConfig;
}

const Configuracoes = () => {
  const [settings, setSettings] = useState<SystemSettings>({});
  const { toast } = useToast();

  // Estado para armazenar os modelos disponíveis
  const [models, setModels] = useState<LLMModel[]>([]);
  const [selectedProvider, setSelectedProvider] =
    useState<LLMProvider>("openai");
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  // Inicializar configurações se não existirem
  const initializeSettings = (data: SystemSettings) => {
    let updatedData = { ...data };

    // Inicializar configurações de monitoramento do YouTube
    if (!updatedData.youtubeMonitoring) {
      updatedData = {
        ...updatedData,
        youtubeMonitoring: {
          scheduleEnabled: false,
          respectPlayerState: false,
          monitoringEnabled: false,
          commentProcessingEnabled: false,
          llmEnabled: false,
          notificationsEnabled: false,
          checkIntervalSeconds: 60,
        },
      };
    }

    // Inicializar configurações de LLM
    if (!updatedData.llm) {
      updatedData = {
        ...updatedData,
        llm: {
          provider: "openai",
          apiKey: "",
          model: "gpt-3.5-turbo",
          temperature: 0.7,
          maxTokens: 500,
          enabled: false,
        },
      };
    }

    return updatedData;
  };

  // Carregar modelos quando o provedor ou a chave da API mudar
  const loadModels = async (provider: LLMProvider, apiKey: string) => {
    if (!apiKey) {
      toast({
        title: "Chave de API não configurada",
        description:
          "Configure uma chave de API para ver os modelos disponíveis.",
        variant: "destructive",
      });
      return;
    }

    setIsLoadingModels(true);
    try {
      const availableModels = await fetchModelsByProvider(provider, apiKey);
      setModels(availableModels);
    } catch (error) {
      console.error(`Erro ao carregar modelos do ${provider}:`, error);
      toast({
        title: `Erro ao carregar modelos do ${provider}`,
        description:
          error instanceof Error ? error.message : "Erro desconhecido",
        variant: "destructive",
      });
      setModels([]);
    } finally {
      setIsLoadingModels(false);
    }
  };

  // Atualizar o provedor selecionado
  const handleProviderChange = (provider: LLMProvider) => {
    setSelectedProvider(provider);

    // Atualizar o provedor nas configurações
    const updatedLLM = {
      ...settings.llm,
      provider,
      model: "", // Resetar o modelo quando o provedor mudar
    };

    setSettings({
      ...settings,
      llm: updatedLLM,
    });

    // Carregar modelos do novo provedor
    if (updatedLLM?.apiKey) {
      loadModels(provider, updatedLLM.apiKey);
    }
  };

  // Carregar configurações do sistema
  const { data, refetch } = useQuery({
    queryKey: ["settings"],
    queryFn: async () => {
      const docRef = doc(db, "system", "settings");
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        const data = docSnap.data() as SystemSettings;
        console.log("Document data:", data);
        setSettings(data);
        if (data.llm?.provider) {
          setSelectedProvider(data.llm.provider);
        }
        return initializeSettings(data);
      }
      return initializeSettings({});
    },
    initialData: initializeSettings({}) as SystemSettings,
  });

  // Carregar modelos quando as configurações forem carregadas ou quando o provedor mudar
  useEffect(() => {
    if (settings.llm?.apiKey && settings.llm?.provider) {
      loadModels(settings.llm.provider, settings.llm.apiKey);
    }
  }, [settings.llm?.provider, settings.llm?.apiKey]);

  const handleSave = async () => {
    try {
      await setDoc(doc(db, "system", "settings"), settings);
      toast({
        title: "Configurações salvas com sucesso!",
      });
      refetch();
    } catch (error) {
      console.error("Erro ao salvar configurações:", error);
      toast({
        variant: "destructive",
        title: "Erro ao salvar configurações",
        description: "Tente novamente.",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Configurações do Sistema</h1>
      </div>

      <Tabs defaultValue="youtube" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="youtube">YouTube</TabsTrigger>
          <TabsTrigger value="llm">IA</TabsTrigger>
          <TabsTrigger value="instagram">Instagram</TabsTrigger>
          <TabsTrigger value="notifications">Notificações</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="other">Outros</TabsTrigger>
        </TabsList>
        <TabsContent value="youtube">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Youtube className="h-5 w-5" />
                Configurações do YouTube
              </CardTitle>
              <CardDescription>
                Configure as credenciais para integração com o YouTube
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">ID do Canal</label>
                <Input
                  value={settings.youtubeChannelId || ""}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      youtubeChannelId: e.target.value,
                    })
                  }
                  placeholder="Ex: UC..."
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Chave da API</label>
                <Input
                  type="password"
                  value={settings.youtubeApiKey || ""}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      youtubeApiKey: e.target.value,
                    })
                  }
                  placeholder="Sua chave da API do YouTube"
                />
              </div>

              <div className="pt-4 border-t">
                <h3 className="text-sm font-medium mb-3">
                  Monitoramento de Transmissões ao Vivo
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Ativar monitoramento</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={
                          settings.youtubeMonitoring?.monitoringEnabled || false
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            youtubeMonitoring: {
                              ...settings.youtubeMonitoring!,
                              monitoringEnabled: e.target.checked,
                            },
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">
                      Usar agendamento baseado nos horários dos cultos
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={
                          settings.youtubeMonitoring?.scheduleEnabled || false
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            youtubeMonitoring: {
                              ...settings.youtubeMonitoring!,
                              scheduleEnabled: e.target.checked,
                            },
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">
                      Respeitar estado do player para encerramento
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={
                          settings.youtubeMonitoring?.respectPlayerState ||
                          false
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            youtubeMonitoring: {
                              ...settings.youtubeMonitoring!,
                              respectPlayerState: e.target.checked,
                            },
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">Processar comentários</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={
                          settings.youtubeMonitoring
                            ?.commentProcessingEnabled || false
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            youtubeMonitoring: {
                              ...settings.youtubeMonitoring!,
                              commentProcessingEnabled: e.target.checked,
                            },
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">
                      Usar IA para processar comentários
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={
                          settings.youtubeMonitoring?.llmEnabled || false
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            youtubeMonitoring: {
                              ...settings.youtubeMonitoring!,
                              llmEnabled: e.target.checked,
                            },
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">Ativar notificações</label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={
                          settings.youtubeMonitoring?.notificationsEnabled ||
                          false
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            youtubeMonitoring: {
                              ...settings.youtubeMonitoring!,
                              notificationsEnabled: e.target.checked,
                            },
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Intervalo de verificação (segundos)
                    </label>
                    <Input
                      type="number"
                      min="30"
                      value={
                        settings.youtubeMonitoring?.checkIntervalSeconds || 60
                      }
                      onChange={(e) =>
                        setSettings({
                          ...settings,
                          youtubeMonitoring: {
                            ...settings.youtubeMonitoring!,
                            checkIntervalSeconds:
                              parseInt(e.target.value) || 60,
                          },
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="llm">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Configurações de Inteligência Artificial
              </CardTitle>
              <CardDescription>
                Configure as credenciais para integração com provedores de LLM
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">
                    Ativar processamento com IA
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      className="h-4 w-4"
                      checked={settings.llm?.enabled || false}
                      onChange={(e) =>
                        setSettings({
                          ...settings,
                          llm: {
                            ...settings.llm!,
                            enabled: e.target.checked,
                          },
                        })
                      }
                    />
                  </div>
                </div>

                <Tabs
                  defaultValue={settings.llm?.provider || "openai"}
                  onValueChange={(value) =>
                    handleProviderChange(value as LLMProvider)
                  }
                >
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger
                      value="openai"
                      className="flex items-center gap-2"
                    >
                      <Bot className="h-4 w-4" />
                      OpenAI
                    </TabsTrigger>
                    <TabsTrigger
                      value="openrouter"
                      className="flex items-center gap-2"
                    >
                      <Sparkles className="h-4 w-4" />
                      OpenRouter
                    </TabsTrigger>
                    <TabsTrigger
                      value="groq"
                      className="flex items-center gap-2"
                    >
                      <Cpu className="h-4 w-4" />
                      Groq
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="openai" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Chave da API OpenAI
                      </label>
                      <Input
                        type="password"
                        value={
                          settings.llm?.provider === "openai"
                            ? settings.llm?.apiKey || ""
                            : ""
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            llm: {
                              ...settings.llm!,
                              provider: "openai",
                              apiKey: e.target.value,
                            },
                          })
                        }
                        placeholder="sk-..."
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="openrouter" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Chave da API OpenRouter
                      </label>
                      <Input
                        type="password"
                        value={
                          settings.llm?.provider === "openrouter"
                            ? settings.llm?.apiKey || ""
                            : ""
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            llm: {
                              ...settings.llm!,
                              provider: "openrouter",
                              apiKey: e.target.value,
                            },
                          })
                        }
                        placeholder="sk-or-..."
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="groq" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Chave da API Groq
                      </label>
                      <Input
                        type="password"
                        value={
                          settings.llm?.provider === "groq"
                            ? settings.llm?.apiKey || ""
                            : ""
                        }
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            llm: {
                              ...settings.llm!,
                              provider: "groq",
                              apiKey: e.target.value,
                            },
                          })
                        }
                        placeholder="gsk_..."
                      />
                    </div>
                  </TabsContent>
                </Tabs>

                {/* Seleção de modelo */}
                {settings.llm?.apiKey && (
                  <div className="space-y-2 pt-4">
                    <label className="text-sm font-medium">Modelo</label>
                    <Select
                      value={settings.llm?.model || ""}
                      onValueChange={(value) =>
                        setSettings({
                          ...settings,
                          llm: {
                            ...settings.llm!,
                            model: value,
                          },
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um modelo" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingModels ? (
                          <div className="p-2 text-center">
                            Carregando modelos...
                          </div>
                        ) : models.length === 0 ? (
                          <div className="p-2 text-center">
                            Nenhum modelo disponível
                          </div>
                        ) : (
                          models.map((model) => (
                            <SelectItem
                              key={model.id}
                              value={model.id}
                              className="flex items-center justify-between"
                            >
                              <div className="flex items-center gap-2">
                                {model.name}
                                <div className="flex gap-1">
                                  {model.isRecommended && (
                                    <Badge
                                      variant="outline"
                                      className="ml-2 bg-green-100 text-green-800 border-green-200"
                                    >
                                      Recomendado
                                    </Badge>
                                  )}
                                  {model.isFree && (
                                    <Badge
                                      variant="outline"
                                      className="ml-2 bg-blue-100 text-blue-800 border-blue-200"
                                    >
                                      Gratuito
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Configurações avançadas */}
                <div className="space-y-4 pt-4 border-t">
                  <h3 className="text-sm font-medium">
                    Configurações avançadas
                  </h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Temperatura</label>
                      <Input
                        type="number"
                        min="0"
                        max="2"
                        step="0.1"
                        value={settings.llm?.temperature || 0.7}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            llm: {
                              ...settings.llm!,
                              temperature: parseFloat(e.target.value) || 0.7,
                            },
                          })
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Máximo de tokens
                      </label>
                      <Input
                        type="number"
                        min="100"
                        max="4000"
                        step="100"
                        value={settings.llm?.maxTokens || 500}
                        onChange={(e) =>
                          setSettings({
                            ...settings,
                            llm: {
                              ...settings.llm!,
                              maxTokens: parseInt(e.target.value) || 500,
                            },
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="instagram">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Instagram className="h-5 w-5" />
                Configurações do Instagram
              </CardTitle>
              <CardDescription>
                Configure as credenciais para integração com o Instagram
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Token de Acesso</label>
                <Input
                  type="password"
                  value={settings.instagramAccessToken || ""}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      instagramAccessToken: e.target.value,
                    })
                  }
                  placeholder="Seu token de acesso do Instagram"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Configurações de Notificações
              </CardTitle>
              <CardDescription>
                Configure as notificações para o sistema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">Brevemente...</CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <TemplateManager />
        </TabsContent>
      </Tabs>

      <Button onClick={handleSave} className="w-full">
        Salvar Configurações
      </Button>
      <div className=""></div>
    </div>
  );
};

export default Configuracoes;


import { useQuery } from "@tanstack/react-query";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useParams } from "react-router-dom";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar, MapPin } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Event } from "@/types/event";
import { Congregation } from "@/types/congregation";

const EventoDetalhes = () => {
  const { id } = useParams();

  const { data: event, isLoading } = useQuery({
    queryKey: ["event", id],
    queryFn: async () => {
      if (!id) return null;
      const docRef = doc(db, "events", id);
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) return null;
      return { id: docSnap.id, ...docSnap.data() } as Event;
    },
  });

  const { data: congregation } = useQuery({
    queryKey: ["congregation", event?.congregationId],
    queryFn: async () => {
      if (!event?.congregationId) return null;
      const docRef = doc(db, "congregacoes", event.congregationId);
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) return null;
      return { id: docSnap.id, ...docSnap.data() } as Congregation;
    },
    enabled: !!event?.congregationId,
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-[200px] w-full" />
      </div>
    );
  }

  if (!event) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold">Evento não encontrado</h1>
        <p className="text-muted-foreground">
          O evento que você está procurando não existe ou foi removido.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{event.title}</h1>
        <p className="text-muted-foreground">Detalhes do evento</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Data e Horário
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg">
              {format(new Date(event.date), "PPP", { locale: ptBR })}
            </p>
            <p className="text-lg">{event.time}</p>
          </CardContent>
        </Card>

        {congregation && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Local
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-lg">{congregation.nome}</p>
              <p className="text-muted-foreground">{congregation.endereco}</p>
            </CardContent>
          </Card>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Descrição</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-lg whitespace-pre-wrap">{event.description}</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default EventoDetalhes;

import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';

interface PermissionGateProps {
  children: React.ReactNode;
  permission?: string;
  role?: string;
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean; // Se true, requer todas as permissões/roles. Se false, requer apenas uma
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * Componente para controlar a exibição de elementos baseado em permissões/roles
 * 
 * @param permission - Permissão única necessária
 * @param role - Role única necessária
 * @param permissions - Lista de permissões (por padrão, requer apenas uma)
 * @param roles - Lista de roles (por padrão, requer apenas uma)
 * @param requireAll - Se true, requer todas as permissões/roles listadas
 * @param fallback - Componente a ser exibido quando não tem permissão
 * @param showFallback - Se deve mostrar o fallback ou apenas ocultar
 */
export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permission,
  role,
  permissions = [],
  roles = [],
  requireAll = false,
  fallback = null,
  showFallback = false
}) => {
  const { hasPermission, hasRole, hasAnyPermission, hasAllPermissions } = usePermissions();

  // Verificar permissão única
  if (permission && !hasPermission(permission)) {
    return showFallback ? <>{fallback}</> : null;
  }

  // Verificar role única
  if (role && !hasRole(role)) {
    return showFallback ? <>{fallback}</> : null;
  }

  // Verificar múltiplas permissões
  if (permissions.length > 0) {
    const hasRequiredPermissions = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
    
    if (!hasRequiredPermissions) {
      return showFallback ? <>{fallback}</> : null;
    }
  }

  // Verificar múltiplas roles
  if (roles.length > 0) {
    const hasRequiredRoles = requireAll
      ? roles.every(r => hasRole(r))
      : roles.some(r => hasRole(r));
    
    if (!hasRequiredRoles) {
      return showFallback ? <>{fallback}</> : null;
    }
  }

  return <>{children}</>;
};

// Hook para usar dentro de componentes funcionais
export const usePermissionGate = () => {
  const permissions = usePermissions();

  const canRender = (options: Omit<PermissionGateProps, 'children' | 'fallback' | 'showFallback'>) => {
    const {
      permission,
      role,
      permissions: permissionList = [],
      roles = [],
      requireAll = false
    } = options;

    // Verificar permissão única
    if (permission && !permissions.hasPermission(permission)) {
      return false;
    }

    // Verificar role única
    if (role && !permissions.hasRole(role)) {
      return false;
    }

    // Verificar múltiplas permissões
    if (permissionList.length > 0) {
      const hasRequiredPermissions = requireAll 
        ? permissions.hasAllPermissions(permissionList)
        : permissions.hasAnyPermission(permissionList);
      
      if (!hasRequiredPermissions) {
        return false;
      }
    }

    // Verificar múltiplas roles
    if (roles.length > 0) {
      const hasRequiredRoles = requireAll
        ? roles.every(r => permissions.hasRole(r))
        : roles.some(r => permissions.hasRole(r));
      
      if (!hasRequiredRoles) {
        return false;
      }
    }

    return true;
  };

  return { canRender, ...permissions };
};

// Componentes específicos para casos comuns
export const AdminOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate role="admin" fallback={fallback} showFallback={!!fallback}>
    {children}
  </PermissionGate>
);

export const ModeratorOrAdmin: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ 
  children, 
  fallback 
}) => (
  <PermissionGate roles={['admin', 'moderator']} fallback={fallback} showFallback={!!fallback}>
    {children}
  </PermissionGate>
);

export const AuthenticatedOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ 
  children, 
  fallback 
}) => {
  const { isAuthenticated } = usePermissions();
  
  if (!isAuthenticated) {
    return fallback ? <>{fallback}</> : null;
  }
  
  return <>{children}</>;
};

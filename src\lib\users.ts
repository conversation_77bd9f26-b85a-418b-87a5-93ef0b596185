import {
  createUserWithEmailAndPassword,
  deleteUser as deleteFirebaseUser,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth';
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  collection,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';
import { auth, db } from './firebase';
import {
  User,
  Role,
  CreateUserRequest,
  UpdateUserRequest,
  SystemRoles,
  SystemPermissions,
  Permission
} from '../types/user';
import {
  sanitizeInput,
  validateUserData,
  logSecurityEvent,
  checkRateLimit,
  canUserModifyUser
} from './security';

// Função para criar usuário no Firebase Auth e Firestore
export const createUser = async (userData: CreateUserRequest, currentUserId: string): Promise<User> => {
  try {
    // Verificar rate limiting
    if (!checkRateLimit(currentUserId, 'create_user')) {
      throw new Error('Muitas tentativas de criação de usuário. Tente novamente mais tarde.');
    }

    // Sanitizar e validar dados
    const sanitizedData = {
      displayName: sanitizeInput(userData.displayName),
      email: userData.email.toLowerCase().trim(),
      roles: userData.roles
    };

    const validation = validateUserData(sanitizedData);
    if (!validation.isValid) {
      logSecurityEvent(currentUserId, 'create_user_validation_failed', { errors: validation.errors }, false);
      throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
    }

    // Verificar se email já existe
    const emailExists = await checkEmailExists(sanitizedData.email);
    if (emailExists) {
      logSecurityEvent(currentUserId, 'create_user_email_exists', { email: sanitizedData.email }, false);
      throw new Error('Este email já está sendo usado por outro usuário.');
    }

    // Criar usuário no Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      sanitizedData.email,
      userData.password
    );

    const firebaseUser = userCredential.user;

    // Atualizar perfil no Firebase Auth
    await updateProfile(firebaseUser, {
      displayName: sanitizedData.displayName
    });

    // Criar documento do usuário no Firestore
    const newUser: User = {
      id: firebaseUser.uid,
      email: sanitizedData.email,
      displayName: sanitizedData.displayName,
      roles: sanitizedData.roles,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      createdBy: currentUserId
    };

    await setDoc(doc(db, 'users', firebaseUser.uid), newUser);

    // Log de sucesso
    logSecurityEvent(currentUserId, 'create_user_success', {
      newUserId: firebaseUser.uid,
      email: sanitizedData.email,
      roles: sanitizedData.roles
    }, true);

    return newUser;
  } catch (error) {
    console.error('Erro ao criar usuário:', error);
    logSecurityEvent(currentUserId, 'create_user_error', { error: error.message }, false);
    throw error;
  }
};

// Função para buscar usuário por ID
export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (userDoc.exists()) {
      return userDoc.data() as User;
    }
    return null;
  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    return null;
  }
};

// Função para buscar todos os usuários
export const getAllUsers = async (): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => doc.data() as User);
  } catch (error) {
    console.error('Erro ao buscar usuários:', error);
    return [];
  }
};

// Função para atualizar usuário
export const updateUser = async (userId: string, userData: UpdateUserRequest, currentUserId: string): Promise<void> => {
  try {
    // Verificar rate limiting
    if (!checkRateLimit(currentUserId, 'update_user')) {
      throw new Error('Muitas tentativas de atualização. Tente novamente mais tarde.');
    }

    // Buscar usuário atual para validações
    const currentUserData = await getUserById(userId);
    if (!currentUserData) {
      throw new Error('Usuário não encontrado.');
    }

    // Buscar dados do usuário que está fazendo a alteração
    const modifyingUser = await getUserById(currentUserId);
    if (!modifyingUser) {
      throw new Error('Usuário modificador não encontrado.');
    }

    // Verificar permissões
    const canModify = canUserModifyUser(modifyingUser, currentUserData, 'update');
    if (!canModify.canModify) {
      logSecurityEvent(currentUserId, 'update_user_permission_denied', {
        targetUserId: userId,
        reason: canModify.reason
      }, false);
      throw new Error(canModify.reason || 'Sem permissão para modificar este usuário.');
    }

    // Sanitizar e validar dados
    const sanitizedData: Partial<UpdateUserRequest> = {};

    if (userData.displayName !== undefined) {
      sanitizedData.displayName = sanitizeInput(userData.displayName);
    }

    if (userData.roles !== undefined) {
      sanitizedData.roles = userData.roles;
    }

    if (userData.isActive !== undefined) {
      sanitizedData.isActive = userData.isActive;
    }

    const validation = validateUserData(sanitizedData);
    if (!validation.isValid) {
      logSecurityEvent(currentUserId, 'update_user_validation_failed', {
        errors: validation.errors,
        targetUserId: userId
      }, false);
      throw new Error(`Dados inválidos: ${validation.errors.join(', ')}`);
    }

    const updateData = {
      ...sanitizedData,
      updatedAt: Date.now()
    };

    await updateDoc(doc(db, 'users', userId), updateData);

    // Log de sucesso
    logSecurityEvent(currentUserId, 'update_user_success', {
      targetUserId: userId,
      changes: sanitizedData
    }, true);

  } catch (error) {
    console.error('Erro ao atualizar usuário:', error);
    logSecurityEvent(currentUserId, 'update_user_error', {
      targetUserId: userId,
      error: error.message
    }, false);
    throw error;
  }
};

// Função para desativar usuário (soft delete)
export const deactivateUser = async (userId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, 'users', userId), {
      isActive: false,
      updatedAt: Date.now()
    });
  } catch (error) {
    console.error('Erro ao desativar usuário:', error);
    throw error;
  }
};

// Função para criar ou atualizar perfil do usuário no primeiro login
export const createOrUpdateUserProfile = async (firebaseUser: FirebaseUser): Promise<User> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));

    if (userDoc.exists()) {
      // Usuário já existe, atualizar último login
      const userData = userDoc.data() as User;
      await updateDoc(doc(db, 'users', firebaseUser.uid), {
        lastLoginAt: Date.now(),
        updatedAt: Date.now()
      });
      return { ...userData, lastLoginAt: Date.now() };
    } else {
      // Primeiro login, criar perfil com role padrão
      const newUser: User = {
        id: firebaseUser.uid,
        email: firebaseUser.email || '',
        displayName: firebaseUser.displayName || firebaseUser.email || '',
        roles: [SystemRoles.USER], // Role padrão
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: firebaseUser.uid, // Auto-criado
        lastLoginAt: Date.now()
      };

      await setDoc(doc(db, 'users', firebaseUser.uid), newUser);
      return newUser;
    }
  } catch (error) {
    console.error('Erro ao criar/atualizar perfil do usuário:', error);
    throw error;
  }
};

// Função para buscar todas as roles
export const getAllRoles = async (): Promise<Role[]> => {
  try {
    const rolesQuery = query(
      collection(db, 'roles'),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    const querySnapshot = await getDocs(rolesQuery);
    return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Role));
  } catch (error) {
    console.error('Erro ao buscar roles:', error);
    return [];
  }
};

// Função para verificar se usuário tem permissão específica
export const hasPermission = (user: User, roles: Role[], permission: string): boolean => {
  if (!user || !user.isActive) return false;

  // Admin sempre tem todas as permissões
  if (user.roles.includes(SystemRoles.ADMIN)) return true;

  // Verificar permissões específicas das roles do usuário
  const userRoles = roles.filter(role => user.roles.includes(role.id));

  return userRoles.some(role =>
    role.permissions.some(p => p.id === permission)
  );
};

// Função para verificar se usuário tem role específica
export const hasRole = (user: User, role: string): boolean => {
  if (!user || !user.isActive) return false;
  return user.roles.includes(role);
};

// Função para excluir role (não pode ser deletada se houver usuários usando)
export const deleteRole = async (roleId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'roles', roleId));
  } catch (error) {
    console.error('Erro ao excluir role:', error);
    throw error;
  }
};

// Função para inicializar roles padrão do sistema
export const initializeSystemRoles = async (): Promise<void> => {
  try {
    const systemRoles = [
      {
        id: SystemRoles.ADMIN,
        name: 'Administrador',
        description: 'Acesso total ao sistema',
        permissions: Object.values(SystemPermissions).map(p => ({ id: p, name: p, description: p, resource: '', action: '' })),
        isActive: true,
        isSystemRole: true,
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      {
        id: SystemRoles.MODERATOR,
        name: 'Moderador',
        description: 'Pode gerenciar pedidos de oração e eventos',
        permissions: [
          SystemPermissions.PRAYERS_CREATE,
          SystemPermissions.PRAYERS_READ,
          SystemPermissions.PRAYERS_UPDATE,
          SystemPermissions.PRAYERS_MODERATE,
          SystemPermissions.EVENTS_CREATE,
          SystemPermissions.EVENTS_READ,
          SystemPermissions.EVENTS_UPDATE,
          SystemPermissions.MEMBERS_READ
        ].map(p => ({ id: p, name: p, description: p, resource: '', action: '' })),
        isActive: true,
        isSystemRole: true,
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      {
        id: SystemRoles.USER,
        name: 'Usuário',
        description: 'Acesso básico ao sistema',
        permissions: [
          SystemPermissions.PRAYERS_READ,
          SystemPermissions.EVENTS_READ,
          SystemPermissions.MEMBERS_READ
        ].map(p => ({ id: p, name: p, description: p, resource: '', action: '' })),
        isActive: true,
        isSystemRole: true,
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      {
        id: SystemRoles.VIEWER,
        name: 'Visualizador',
        description: 'Apenas leitura',
        permissions: [
          SystemPermissions.PRAYERS_READ,
          SystemPermissions.EVENTS_READ
        ].map(p => ({ id: p, name: p, description: p, resource: '', action: '' })),
        isActive: true,
        isSystemRole: true,
        createdAt: Date.now(),
        updatedAt: Date.now()
      }
    ];

    for (const role of systemRoles) {
      const roleDoc = await getDoc(doc(db, 'roles', role.id));
      if (!roleDoc.exists()) {
        await setDoc(doc(db, 'roles', role.id), role);
      }
    }
  } catch (error) {
    console.error('Erro ao inicializar roles do sistema:', error);
  }
};

// Função para buscar usuários ativos
export const getActiveUsers = async (): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => doc.data() as User);
  } catch (error) {
    console.error('Erro ao buscar usuários ativos:', error);
    return [];
  }
};

// Função para buscar usuários por role
export const getUsersByRole = async (roleId: string): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('roles', 'array-contains', roleId),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => doc.data() as User);
  } catch (error) {
    console.error('Erro ao buscar usuários por role:', error);
    return [];
  }
};

// Função para reativar usuário
export const reactivateUser = async (userId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, 'users', userId), {
      isActive: true,
      updatedAt: Date.now()
    });
  } catch (error) {
    console.error('Erro ao reativar usuário:', error);
    throw error;
  }
};

// Função para verificar se email já existe
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('email', '==', email)
    );
    const querySnapshot = await getDocs(usersQuery);
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Erro ao verificar email:', error);
    return false;
  }
};

// Função para criar role personalizada
export const createRole = async (roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> => {
  try {
    const id = roleData.name.toLowerCase().replace(/\s/g, '-');
    const newRole: Role = {
      ...roleData,
      id,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    const roleRef = doc(db, 'roles', id);

    await setDoc(roleRef, newRole);
    return newRole;
  } catch (error) {
    console.error('Erro ao criar role:', error);
    throw error;
  }
};

// Função para atualizar role
export const updateRole = async (roleId: string, roleData: Partial<Role>): Promise<void> => {
  try {
    const updateData = {
      ...roleData,
      updatedAt: Date.now()
    };

    await updateDoc(doc(db, 'roles', roleId), updateData);
  } catch (error) {
    console.error('Erro ao atualizar role:', error);
    throw error;
  }
};

// Função para desativar role (não pode ser deletada se houver usuários usando)
export const deactivateRole = async (roleId: string): Promise<void> => {
  try {
    // Verificar se há usuários usando esta role
    const usersWithRole = await getUsersByRole(roleId);
    if (usersWithRole.length > 0) {
      throw new Error('Não é possível desativar uma role que está sendo usada por usuários');
    }

    await updateDoc(doc(db, 'roles', roleId), {
      isActive: false,
      updatedAt: Date.now()
    });
  } catch (error) {
    console.error('Erro ao desativar role:', error);
    throw error;
  }
};

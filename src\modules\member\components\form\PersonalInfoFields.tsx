import { Input } from "@/components/ui/input";
import { FormField } from "@/components/ui/form";

interface PersonalInfoFieldsProps {
  register: any;
}

export const PersonalInfoFields = ({ register }: PersonalInfoFieldsProps) => {
  return (
    <>
      <div className="space-y-2">
        <label htmlFor="nome" className="text-sm font-medium">
          Nome Completo
        </label>
        <Input
          id="nome"
          {...register("nome", { required: true })}
          placeholder="Digite o nome completo"
          className="transition-all focus:scale-[1.01]"
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="dataNascimento" className="text-sm font-medium">
            Data de Nascimento
          </label>
          <Input
            id="dataNascimento"
            type="date"
            {...register("dataNascimento", { required: true })}
            className="transition-all focus:scale-[1.01]"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="dataBatismo" className="text-sm font-medium">
            Data de Batismo
          </label>
          <Input
            id="dataBatismo"
            type="date"
            {...register("dataBatismo")}
            className="transition-all focus:scale-[1.01]"
          />
        </div>
      </div>
    </>
  );
};
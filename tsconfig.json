{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*",
        "./node_modules/*",
        "./lib/*"
      ]
    },
    "noImplicitAny": false,
    "noUnusedParameters": false,
    "skipLibCheck": true,
    "allowJs": true,
    "noUnusedLocals": false,
    "strictNullChecks": false,
    // "module": "ESNext",
    // "moduleResolution": "nodenext",
    // "target": "esnext",
    // "esModuleInterop": true,
    // "strict": true,
    // "skipDefaultLibCheck": true
  }
}
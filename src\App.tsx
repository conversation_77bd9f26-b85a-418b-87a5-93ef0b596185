import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from "@tanstack/react-query";
import { ThemeProvider } from "next-themes";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { Navigation } from "./components/Navigation";
import ProtectedRoute from "./components/ProtectedRoute";
import { modules } from "./modules";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Public from "./pages/Public";
import EventoDetalhes from "./modules/events/pages/EventoDetalhes";
import { QuickControlsButton } from "./components/youtube/QuickControlsButton";
import { db } from "./lib/firebase";
import { doc, getDoc } from "firebase/firestore";
import { AuthProvider } from "./contexts/AuthContext";
import { UserProvider } from "./contexts/UserContext";
import { SystemPermissions } from "./types/user";

const queryClient = new QueryClient();

const Layout = ({ children }: { children: React.ReactNode }) => {
  // Buscar configurações do YouTube para inicializar os botões de controle rápido
  const { data: settings } = useQuery({
    queryKey: ["youtube-monitoring-settings"],
    queryFn: async () => {
      const docRef = doc(db, "system", "settings");
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        return docSnap.data();
      }
      return {};
    },
  });

  return (
    <div className="flex min-h-screen">
      <Navigation />
      <main className="flex-1 ml-[var(--sidebar-width)] transition-[margin] duration-200 ease-linear group-data-[state=collapsed]/sidebar-wrapper:ml-[var(--sidebar-width-icon)]">
        <div className="w-full p-6">{children}</div>
      </main>
      <QuickControlsButton
        initialMonitoringEnabled={
          settings?.youtubeMonitoring?.monitoringEnabled
        }
        initialCommentProcessingEnabled={
          settings?.youtubeMonitoring?.commentProcessingEnabled
        }
      />
    </div>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider attribute="class" defaultTheme="dark">
      <TooltipProvider>
        <AuthProvider>
          <UserProvider>
            <BrowserRouter>
              <div className="relative">
                <Toaster />
                <Sonner />
                <Routes>
                  <Route path="/" element={<Public />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/eventos/:id" element={<EventoDetalhes />} />
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Layout>
                          <Index />
                        </Layout>
                      </ProtectedRoute>
                    }
                  />

                  {modules.map((module) =>
                    module.routes.map((route) => {
                      // Definir permissões necessárias para cada rota
                      let requiredPermission: string | undefined;

                      if (route.path.startsWith('/usuarios')) {
                        requiredPermission = SystemPermissions.USERS_READ;
                      } else if (route.path.startsWith('/pedidos')) {
                        requiredPermission = SystemPermissions.PRAYERS_READ;
                      } else if (route.path.startsWith('/eventos')) {
                        requiredPermission = SystemPermissions.EVENTS_READ;
                      } else if (route.path.startsWith('/membros')) {
                        requiredPermission = SystemPermissions.MEMBERS_READ;
                      } else if (route.path.startsWith('/dashboard/settings')) {
                        requiredPermission = SystemPermissions.SETTINGS_MANAGE;
                      }

                      return (
                        <Route
                          key={route.path}
                          path={route.path}
                          element={
                            <ProtectedRoute requiredPermission={requiredPermission}>
                              <Layout>
                                <route.component />
                              </Layout>
                            </ProtectedRoute>
                          }
                        />
                      );
                    })
                  )}
                </Routes>
              </div>
            </BrowserRouter>
          </UserProvider>
        </AuthProvider>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;

# Sistema de Gerenciamento de Usuários

## Visão Geral

O sistema de gerenciamento de usuários foi implementado para fornecer controle de acesso granular e seguro ao sistema Belém Connect. Ele integra-se com o Firebase Auth existente e adiciona camadas de autorização baseadas em roles e permissões.

## Arquitetura

### Componentes Principais

1. **AuthContext** (`src/contexts/AuthContext.tsx`)
   - Gerencia o estado de autenticação do usuário
   - Integra com Firebase Auth
   - Calcula permissões em tempo real

2. **UserContext** (`src/contexts/UserContext.tsx`)
   - Gerencia operações CRUD de usuários
   - Controla validações de segurança
   - Fornece interface para gerenciamento de usuários

3. **ProtectedRoute** (`src/components/ProtectedRoute.tsx`)
   - Componente aprimorado para proteção de rotas
   - Suporte a verificação de permissões específicas
   - Suporte a verificação de roles específicas

4. **PermissionGate** (`src/components/PermissionGate.tsx`)
   - Controle de exibição de componentes baseado em permissões
   - Suporte a múltiplas permissões/roles
   - Componentes especializados (AdminOnly, ModeratorOrAdmin, etc.)

### Estrutura de Dados

#### Firestore Collections

```
users/
  {uid}/
    email: string
    displayName: string
    roles: string[]
    isActive: boolean
    createdAt: number
    updatedAt: number
    createdBy: string
    lastLoginAt?: number

roles/
  {roleId}/
    name: string
    description: string
    permissions: Permission[]
    isActive: boolean
    isSystemRole: boolean
    createdAt: number
    updatedAt: number
```

#### Tipos TypeScript

- `User`: Interface principal do usuário
- `Role`: Interface de roles
- `Permission`: Interface de permissões
- `UserPermissions`: Permissões calculadas do usuário
- `SystemRoles`: Enum com roles padrão do sistema
- `SystemPermissions`: Enum com permissões do sistema

## Sistema de Roles

### Roles Padrão

1. **Admin** (`admin`)
   - Acesso total ao sistema
   - Pode gerenciar usuários, roles e configurações
   - Não pode ser removido ou desativado

2. **Moderator** (`moderator`)
   - Pode gerenciar pedidos de oração e eventos
   - Pode visualizar membros
   - Não pode gerenciar usuários

3. **User** (`user`)
   - Acesso básico ao sistema
   - Pode visualizar pedidos, eventos e membros
   - Role padrão para novos usuários

4. **Viewer** (`viewer`)
   - Apenas leitura
   - Acesso limitado a visualização

### Permissões

As permissões seguem o padrão `recurso:ação`:

- `users:create` - Criar usuários
- `users:read` - Visualizar usuários
- `users:update` - Editar usuários
- `users:delete` - Excluir usuários
- `roles:manage` - Gerenciar roles
- `prayers:*` - Permissões para pedidos de oração
- `events:*` - Permissões para eventos
- `members:*` - Permissões para membros
- `settings:manage` - Gerenciar configurações
- `system:admin` - Administração do sistema

## Segurança

### Validações Implementadas

1. **Sanitização de Dados**
   - Remoção de caracteres HTML
   - Limitação de tamanho de campos
   - Normalização de emails

2. **Validação de Senhas**
   - Mínimo 8 caracteres
   - Pelo menos uma letra maiúscula
   - Pelo menos uma letra minúscula
   - Pelo menos um número
   - Pelo menos um caractere especial

3. **Controle de Acesso**
   - Verificação de permissões em tempo real
   - Validação de modificação de usuários
   - Proteção contra auto-modificação destrutiva

4. **Rate Limiting**
   - Controle de tentativas de criação/modificação
   - Prevenção de ataques de força bruta

5. **Auditoria**
   - Log de todas as operações de segurança
   - Rastreamento de tentativas suspeitas

### Regras de Negócio

1. **Criação de Usuários**
   - Apenas admins podem criar usuários
   - Email deve ser único
   - Senha deve atender critérios de segurança
   - Usuário deve ter pelo menos uma role

2. **Modificação de Usuários**
   - Usuários não podem modificar próprios roles
   - Apenas admins podem modificar outros admins
   - Moderadores não podem modificar outros moderadores
   - Validação de permissões antes de qualquer alteração

3. **Exclusão de Usuários**
   - Soft delete (desativação)
   - Usuários não podem se auto-excluir
   - Apenas admins podem excluir usuários

## Como Usar

### Verificar Permissões

```tsx
import { usePermissions } from '@/hooks/usePermissions';

const MyComponent = () => {
  const { canCreateUsers, hasPermission } = usePermissions();
  
  if (canCreateUsers) {
    // Mostrar botão de criar usuário
  }
  
  if (hasPermission('events:create')) {
    // Mostrar funcionalidade de criar evento
  }
};
```

### Proteger Rotas

```tsx
<ProtectedRoute requiredPermission="users:read">
  <UserListPage />
</ProtectedRoute>

<ProtectedRoute requiredRole="admin">
  <AdminPanel />
</ProtectedRoute>
```

### Controlar Exibição de Componentes

```tsx
import { PermissionGate, AdminOnly } from '@/components/PermissionGate';

// Verificar permissão específica
<PermissionGate permission="users:create">
  <CreateUserButton />
</PermissionGate>

// Verificar múltiplas permissões (qualquer uma)
<PermissionGate permissions={['users:create', 'users:update']}>
  <UserActions />
</PermissionGate>

// Apenas para admins
<AdminOnly>
  <DangerousAction />
</AdminOnly>
```

### Gerenciar Usuários

```tsx
import { useUsers } from '@/contexts/UserContext';

const UserManagement = () => {
  const { users, createUser, updateUser, deleteUser } = useUsers();
  
  const handleCreateUser = async (userData) => {
    await createUser({
      email: userData.email,
      password: userData.password,
      displayName: userData.displayName,
      roles: ['user']
    });
  };
};
```

## Configuração Inicial

### 1. Inicialização das Roles

As roles padrão são criadas automaticamente na primeira execução do sistema através da função `initializeSystemRoles()`.

### 2. Primeiro Usuário Admin

Para criar o primeiro usuário admin, você pode:

1. Criar um usuário normalmente através do Firebase Auth
2. Manualmente adicionar o documento na coleção `users` com role `admin`
3. Ou configurar emails de admin no sistema de configurações

### 3. Configuração de Permissões

As permissões são definidas estaticamente no código, mas podem ser expandidas conforme necessário.

## Manutenção

### Adicionar Nova Permissão

1. Adicionar ao enum `SystemPermissions` em `src/types/user.ts`
2. Atualizar as roles relevantes em `initializeSystemRoles()`
3. Adicionar verificação no hook `usePermissions()`
4. Implementar controle de acesso onde necessário

### Adicionar Nova Role

1. Adicionar ao enum `SystemRoles` (se for role do sistema)
2. Definir permissões da role
3. Atualizar função `initializeSystemRoles()`
4. Adicionar validações necessárias

### Monitoramento

- Verificar logs de auditoria regularmente
- Monitorar tentativas de acesso negado
- Revisar usuários inativos periodicamente
- Validar integridade das permissões

## Troubleshooting

### Problemas Comuns

1. **Usuário não consegue acessar funcionalidade**
   - Verificar se tem a role necessária
   - Verificar se a role tem a permissão necessária
   - Verificar se o usuário está ativo

2. **Erro ao criar usuário**
   - Verificar se email já existe
   - Verificar força da senha
   - Verificar permissões do usuário criador

3. **Permissões não atualizando**
   - Verificar se o contexto está sendo usado corretamente
   - Verificar se as roles foram atualizadas no Firestore
   - Fazer refresh do usuário se necessário

### Debug

Use o hook `usePermissions()` para debug:

```tsx
const permissions = usePermissions();
console.log('User permissions:', permissions);
```

import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import UserForm from "../components/UserForm";

const CadastroUsuarios = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate("/usuarios");
  };

  const handleCancel = () => {
    navigate("/usuarios");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate("/usuarios")}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Novo Usuário</h1>
          <p className="text-muted-foreground">
            Cadastre um novo usuário no sistema
          </p>
        </div>
      </div>

      <div className="max-w-2xl">
        <UserForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default CadastroUsuarios;

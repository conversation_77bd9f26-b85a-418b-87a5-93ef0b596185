import { Input } from "@/components/ui/input";

interface ContactFieldsProps {
  register: any;
}

export const ContactFields = ({ register }: ContactFieldsProps) => {
  return (
    <>
      <div className="space-y-2">
        <label htmlFor="endereco" className="text-sm font-medium">
          Endereço
        </label>
        <Input
          id="endereco"
          {...register("endereco", { required: true })}
          placeholder="Digite o endereço completo"
          className="transition-all focus:scale-[1.01]"
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="telefone" className="text-sm font-medium">
            Telefone
          </label>
          <Input
            id="telefone"
            {...register("telefone", { required: true })}
            placeholder="Digite o telefone"
            className="transition-all focus:scale-[1.01]"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            E-mail
          </label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="Digite o e-mail"
            className="transition-all focus:scale-[1.01]"
          />
        </div>
      </div>
    </>
  );
};
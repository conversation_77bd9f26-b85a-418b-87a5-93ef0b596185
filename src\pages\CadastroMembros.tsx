import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { addDoc, collection } from "firebase/firestore";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";

interface FormData {
  nome: string;
  dataNascimento: string;
  rg: string;
  cpf: string;
  endereco: string;
  telefone: string;
  email: string;
  dataBatismo?: string;
  congregacao: string;
}

const CadastroMembros = () => {
  const { register, handleSubmit, reset } = useForm<FormData>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      const docRef = await addDoc(collection(db, "membros"), {
        ...data,
        createdAt: new Date().getTime(),
      });

      toast({
        title: "Membro cadastrado com sucesso!",
        description: "O cadastro foi realizado com sucesso.",
      });

      navigate(`/cartao-membro/${docRef.id}`);
      reset();
    } catch (error) {
      console.error("Erro ao cadastrar membro:", error);
      toast({
        variant: "destructive",
        title: "Erro ao cadastrar membro",
        description: "Tente novamente.",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto animate-fade-in">
      <Card>
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-center">Cadastro de Membros</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="nome" className="text-sm font-medium">
                Nome Completo
              </label>
              <Input
                id="nome"
                {...register("nome", { required: true })}
                placeholder="Digite o nome completo"
                className="transition-all focus:scale-[1.01]"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="dataNascimento" className="text-sm font-medium">
                  Data de Nascimento
                </label>
                <Input
                  id="dataNascimento"
                  type="date"
                  {...register("dataNascimento", { required: true })}
                  className="transition-all focus:scale-[1.01]"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="dataBatismo" className="text-sm font-medium">
                  Data de Batismo
                </label>
                <Input
                  id="dataBatismo"
                  type="date"
                  {...register("dataBatismo")}
                  className="transition-all focus:scale-[1.01]"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="rg" className="text-sm font-medium">
                  RG
                </label>
                <Input
                  id="rg"
                  {...register("rg", { required: true })}
                  placeholder="Digite o RG"
                  className="transition-all focus:scale-[1.01]"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="cpf" className="text-sm font-medium">
                  CPF
                </label>
                <Input
                  id="cpf"
                  {...register("cpf", { required: true })}
                  placeholder="Digite o CPF"
                  className="transition-all focus:scale-[1.01]"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="endereco" className="text-sm font-medium">
                Endereço
              </label>
              <Input
                id="endereco"
                {...register("endereco", { required: true })}
                placeholder="Digite o endereço completo"
                className="transition-all focus:scale-[1.01]"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="telefone" className="text-sm font-medium">
                  Telefone
                </label>
                <Input
                  id="telefone"
                  {...register("telefone", { required: true })}
                  placeholder="Digite o telefone"
                  className="transition-all focus:scale-[1.01]"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  E-mail
                </label>
                <Input
                  id="email"
                  type="email"
                  {...register("email")}
                  placeholder="Digite o e-mail"
                  className="transition-all focus:scale-[1.01]"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="congregacao" className="text-sm font-medium">
                Congregação
              </label>
              <Input
                id="congregacao"
                {...register("congregacao", { required: true })}
                placeholder="Digite a congregação"
                className="transition-all focus:scale-[1.01]"
              />
            </div>

            <Button
              type="submit"
              className="w-full transition-all hover:scale-[1.02]"
              disabled={loading}
            >
              {loading ? (
                <span className="flex items-center gap-2">
                  <span className="animate-pulse">Cadastrando...</span>
                </span>
              ) : (
                "Cadastrar Membro"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default CadastroMembros;
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import MemberForm from "../components/MemberForm";
import { MemberStatusToggle } from "../components/MemberStatusToggle";
import { Plus, Pencil } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface Membro {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  congregacaoId: string;
  cargoIds: string[];
  ativo: boolean;
}

const ListagemMembros = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);

  const { data: membros, refetch } = useQuery({
    queryKey: ["membros"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "membros"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Membro[];
    },
  });

  const { data: congregacoes } = useQuery({
    queryKey: ["congregacoes"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "congregacoes"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as any[];
    },
  });

  const { data: cargos } = useQuery({
    queryKey: ["cargos"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "cargos"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as any[];
    },
  });

  const getCongregacaoName = (congregacaoId: string) => {
    const congregacao = congregacoes?.find(
      (congregacao) => congregacao.id === congregacaoId
    );
    return congregacao?.nome || "";
  };

  const getCargosNames = (cargoIds: string[] = []) => {
    return cargos?.filter((cargo) => cargoIds?.includes(cargo.id)).map((cargo) => cargo.nome) || [];
  };

  const handleEdit = (memberId: string) => {
    setSelectedMemberId(memberId);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedMemberId(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Membros</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setSelectedMemberId(null)}>
              <Plus className="mr-2 h-4 w-4" />
              Novo Membro
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>
                {selectedMemberId ? "Editar Membro" : "Cadastro de Membro"}
              </DialogTitle>
              <DialogDescription>
                {selectedMemberId
                  ? "Atualize os dados do membro abaixo"
                  : "Preencha os dados do novo membro abaixo"}
              </DialogDescription>
            </DialogHeader>
            <MemberForm
              memberId={selectedMemberId || undefined}
              onSuccess={() => {
                handleCloseDialog();
                refetch();
              }}
              onCancel={handleCloseDialog}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>E-mail</TableHead>
              <TableHead>Telefone</TableHead>
              <TableHead>Congregação</TableHead>
              <TableHead>Cargos</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {membros?.map((membro) => (
              <TableRow key={membro.id}>
                <TableCell className="font-medium">{membro.nome}</TableCell>
                <TableCell>{membro.email}</TableCell>
                <TableCell>{membro.telefone}</TableCell>
                <TableCell>{getCongregacaoName(membro.congregacaoId)}</TableCell>
                <TableCell>
                  {getCargosNames(membro.cargoIds)?.map((cargo) => (
                    <span
                      key={cargo}
                      className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary mr-1"
                    >
                      {cargo}
                    </span>
                  ))}
                </TableCell>
                <TableCell>
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      membro.ativo
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {membro.ativo ? "Ativo" : "Suspenso"}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEdit(membro.id)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <MemberStatusToggle
                      memberId={membro.id}
                      isActive={membro.ativo}
                      onStatusChange={refetch}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default ListagemMembros;
import React, { useState } from "react";
import { getPermissionDescription, Permission, SystemPermissions } from "@/types/user";
import { Checkbox } from "@/components/ui/checkbox";
import { usePermissions } from "@/hooks/usePermissions";

interface PermissionSelectorProps {
  selectedPermissions: Permission[];
  onChange: (permissions: Permission[]) => void;
}

const PermissionSelector: React.FC<PermissionSelectorProps> = ({
  selectedPermissions,
  onChange
}) => {

  const permissions = Object.values(SystemPermissions).map(p => ({ id: p, name: p, description: p, resource: '', action: '' }));

  const includePermission = (permission: Permission) => {
    return selectedPermissions.find(p => p.id === permission.id) !== undefined;
  };

  const handlePermissionToggle = (permissionId: string) => {
    if (selectedPermissions.find(p => p.id === permissionId)) {
      // Remover permissão
      onChange(selectedPermissions.filter(({ id }) => id !== permissionId));
    } else {
      onChange([...selectedPermissions, permissions.find(p => p.id === permissionId)]);
    }
  };

  return (
    <div className="grid grid-cols-2 gap-2 max-h-60 overflow-y-auto">
      {permissions.map((permission) => (
        <div key={permission.id} className="flex items-start space-x-3">
          <Checkbox
            id={permission.id}
            checked={includePermission(permission)}
            onCheckedChange={() => handlePermissionToggle(permission.id)}
          />
          <div className="grid gap-1.5 leading-none">
            <label
              htmlFor={permission.id}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {getPermissionDescription(permission.id)}
            </label>
            <p className="text-xs text-muted-foreground">
              {permission.description}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PermissionSelector;

interface LeftCardButtonProps {
  onClick: () => void;
  disabled: boolean;
  children: React.ReactNode;
}
export const LeftCardButton = (props: LeftCardButtonProps) => {
  return <div
    onClick={props.disabled ? undefined : props.onClick}
    className={`absolute rounded-l-lg left-0 top-0 bottom-0 w-1/12 bg-primary flex items-center justify-center ${!props.disabled && 'hover:bg-primary/80 cursor-pointer'} transition-colors ${props.disabled && 'opacity-50'}`}
  >
    {props.children}
  </div>
}
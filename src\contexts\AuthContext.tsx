import React, { createContext, useContext, useEffect, useState } from 'react';
import { onAuthStateChanged, signOut, User as FirebaseUser } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { 
  User, 
  Role, 
  AuthContextType, 
  UserPermissions,
  SystemRoles 
} from '@/types/user';
import { 
  createOrUpdateUserProfile, 
  getAllRoles, 
  hasPermission as checkPermission,
  hasRole as checkRole,
  initializeSystemRoles
} from '@/lib/users';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [roles, setRoles] = useState<Role[]>([]);

  // Inicializar roles do sistema na primeira execução
  useEffect(() => {
    initializeSystemRoles();
  }, []);

  // Carregar roles
  const loadRoles = async () => {
    try {
      const rolesData = await getAllRoles();
      setRoles(rolesData);
    } catch (error) {
      console.error('Erro ao carregar roles:', error);
    }
  };

  useEffect(() => {
    loadRoles();
  }, []);

  // Monitorar mudanças de autenticação
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      try {
        if (firebaseUser) {
          // Usuário logado - criar/atualizar perfil
          const userData = await createOrUpdateUserProfile(firebaseUser);
          setUser(userData);
        } else {
          // Usuário deslogado
          setUser(null);
        }
      } catch (error) {
        console.error('Erro ao processar mudança de autenticação:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  // Calcular permissões do usuário
  const permissions: UserPermissions = React.useMemo(() => {
    if (!user || !user.isActive) {
      return {
        canCreateUsers: false,
        canEditUsers: false,
        canDeleteUsers: false,
        canManageRoles: false,
        canViewUsers: false,
        canManagePrayers: false,
        canManageEvents: false,
        canManageSettings: false
      };
    }

    // Admin tem todas as permissões
    if (user.roles.includes(SystemRoles.ADMIN)) {
      return {
        canCreateUsers: true,
        canEditUsers: true,
        canDeleteUsers: true,
        canManageRoles: true,
        canViewUsers: true,
        canManagePrayers: true,
        canManageEvents: true,
        canManageSettings: true
      };
    }

    return {
      canCreateUsers: checkPermission(user, roles, 'users:create'),
      canEditUsers: checkPermission(user, roles, 'users:update'),
      canDeleteUsers: checkPermission(user, roles, 'users:delete'),
      canManageRoles: checkPermission(user, roles, 'roles:manage'),
      canViewUsers: checkPermission(user, roles, 'users:read'),
      canManagePrayers: checkPermission(user, roles, 'prayers:moderate'),
      canManageEvents: checkPermission(user, roles, 'events:create'),
      canManageSettings: checkPermission(user, roles, 'settings:manage')
    };
  }, [user, roles]);

  const hasPermission = (permission: string): boolean => {
    return user ? checkPermission(user, roles, permission) : false;
  };

  const hasRole = (role: string): boolean => {
    return user ? checkRole(user, role) : false;
  };

  const refreshUser = async (): Promise<void> => {
    if (auth.currentUser) {
      const userData = await createOrUpdateUserProfile(auth.currentUser);
      setUser(userData);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await signOut(auth);
      setUser(null);
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    permissions,
    hasPermission,
    hasRole,
    refreshUser,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

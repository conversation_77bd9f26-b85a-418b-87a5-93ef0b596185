import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { useQuery } from "@tanstack/react-query";
import { addDoc, collection, deleteDoc, doc, getDocs, updateDoc } from "firebase/firestore";
import { Clock, Pencil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CongregationService } from "@/types/congregation";

interface Congregacao {
  id: string;
  nome: string;
  endereco: string;
  telefone?: string;
  services?: CongregationService[];
}

const GerenciamentoCongregacoes = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isServicesDialogOpen, setIsServicesDialogOpen] = useState(false);
  const [editingCongregacao, setEditingCongregacao] = useState<Congregacao | null>(null);
  const [nome, setNome] = useState("");
  const [endereco, setEndereco] = useState("");
  const [telefone, setTelefone] = useState("");
  const [services, setServices] = useState<CongregationService[]>([]);
  const { toast } = useToast();

  const { data: congregacoes, refetch } = useQuery({
    queryKey: ["congregacoes"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "congregacoes"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Congregacao[];
    },
  });

  const resetForm = () => {
    setNome("");
    setEndereco("");
    setTelefone("");
    setServices([]);
    setEditingCongregacao(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const congregacaoData = {
        nome,
        endereco,
        telefone,
        services,
        updatedAt: new Date().getTime(),
      };

      if (editingCongregacao) {
        await updateDoc(doc(db, "congregacoes", editingCongregacao.id), congregacaoData);
        toast({
          title: "Congregação atualizada com sucesso!",
        });
      } else {
        await addDoc(collection(db, "congregacoes"), {
          ...congregacaoData,
          createdAt: new Date().getTime(),
        });
        toast({
          title: "Congregação cadastrada com sucesso!",
        });
      }

      resetForm();
      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Erro ao salvar congregação:", error);
      toast({
        variant: "destructive",
        title: "Erro ao salvar congregação",
        description: "Tente novamente.",
      });
    }
  };

  const handleEdit = (congregacao: Congregacao) => {
    setEditingCongregacao(congregacao);
    setNome(congregacao.nome);
    setEndereco(congregacao.endereco);
    setTelefone(congregacao.telefone || "");
    setServices(congregacao.services || []);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteDoc(doc(db, "congregacoes", id));
      toast({
        title: "Congregação removida com sucesso!",
      });
      refetch();
    } catch (error) {
      console.error("Erro ao remover congregação:", error);
      toast({
        variant: "destructive",
        title: "Erro ao remover congregação",
        description: "Tente novamente.",
      });
    }
  };

  const handleAddService = () => {
    setServices([
      ...services,
      {
        dayOfWeek: 0,
        time: "19:00",
        type: "culto",
        description: "Culto de Celebração",
      },
    ]);
  };

  const handleRemoveService = (index: number) => {
    setServices(services.filter((_, i) => i !== index));
  };

  const handleServiceChange = (index: number, field: keyof CongregationService, value: any) => {
    const updatedServices = [...services];
    updatedServices[index] = {
      ...updatedServices[index],
      [field]: value,
    };
    setServices(updatedServices);
  };

  const getDayName = (day: number) => {
    const days = ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"];
    return days[day];
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Congregações</h1>
        <Dialog open={isDialogOpen} onOpenChange={(open) => {
          setIsDialogOpen(open);
          if (!open) resetForm();
        }}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Nova Congregação
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
            <DialogHeader>
              <DialogTitle>{editingCongregacao ? "Editar" : "Cadastro de"} Congregação</DialogTitle>
              <DialogDescription>
                Preencha os dados da congregação abaixo
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4 flex-1 overflow-y-auto pr-4">
              <div className="space-y-2">
                <label htmlFor="nome" className="text-sm font-medium">
                  Nome da Congregação
                </label>
                <Input
                  id="nome"
                  value={nome}
                  onChange={(e) => setNome(e.target.value)}
                  placeholder="Digite o nome da congregação"
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="endereco" className="text-sm font-medium">
                  Endereço
                </label>
                <Input
                  id="endereco"
                  value={endereco}
                  onChange={(e) => setEndereco(e.target.value)}
                  placeholder="Digite o endereço da congregação"
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="telefone" className="text-sm font-medium">
                  Telefone
                </label>
                <Input
                  id="telefone"
                  value={telefone}
                  onChange={(e) => setTelefone(e.target.value)}
                  placeholder="Digite o telefone da congregação"
                />
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium">Horários de Culto</label>
                  <Button type="button" variant="outline" size="sm" onClick={handleAddService}>
                    <Plus className="h-4 w-4 mr-2" />
                    Adicionar Horário
                  </Button>
                </div>
                
                {services.map((service, index) => (
                  <div key={index} className="flex gap-4 items-start border p-4 rounded-lg">
                    <div className="flex-1 space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Dia da Semana</label>
                          <Select
                            value={service.dayOfWeek.toString()}
                            onValueChange={(value) => handleServiceChange(index, "dayOfWeek", parseInt(value))}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                                <SelectItem key={day} value={day.toString()}>
                                  {getDayName(day)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Horário</label>
                          <Input
                            type="time"
                            value={service.time}
                            onChange={(e) => handleServiceChange(index, "time", e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Tipo</label>
                          <Select
                            value={service.type}
                            onValueChange={(value) => handleServiceChange(index, "type", value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="culto">Culto</SelectItem>
                              <SelectItem value="oracao">Oração</SelectItem>
                              <SelectItem value="estudo">Estudo Bíblico</SelectItem>
                              <SelectItem value="outro">Outro</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Descrição</label>
                          <Input
                            value={service.description}
                            onChange={(e) => handleServiceChange(index, "description", e.target.value)}
                            placeholder="Ex: Culto de Celebração"
                          />
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveService(index)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                ))}
              </div>

              <Button type="submit" className="w-full">
                {editingCongregacao ? "Atualizar" : "Cadastrar"} Congregação
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Endereço</TableHead>
              <TableHead>Telefone</TableHead>
              <TableHead>Cultos</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {congregacoes?.map((congregacao) => (
              <TableRow key={congregacao.id}>
                <TableCell className="font-medium">{congregacao.nome}</TableCell>
                <TableCell>{congregacao.endereco}</TableCell>
                <TableCell>{congregacao.telefone || "-"}</TableCell>
                <TableCell>
                  {congregacao.services?.length || 0} horários
                  {congregacao.services && congregacao.services.length > 0 && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="ml-2"
                      onClick={() => {
                        setEditingCongregacao(congregacao);
                        setServices(congregacao.services || []);
                        setIsServicesDialogOpen(true);
                      }}
                    >
                      <Clock className="h-4 w-4" />
                    </Button>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEdit(congregacao)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDelete(congregacao.id)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default GerenciamentoCongregacoes;

export interface YouTubeLiveStream {
  id: string;
  title: string;
  channelId: string;
  channelTitle: string;
  startTime: string;
  endTime?: string; // Timestamp de quando a transmissão foi encerrada
  isLive: boolean;
  videoId: string;
  thumbnailUrl: string;
  updatedAt?: string; // Timestamp da última atualização
}

export interface YouTubeComment {
  id: string;
  authorDisplayName: string;
  authorProfileImageUrl: string;
  textDisplay: string;
  publishedAt: string;
  likeCount: number;
  videoId: string;
  processed: boolean;
}

export interface YouTubeMonitoringSchedule {
  enabled: boolean;
  dayOfWeek: number; // 0-6, onde 0 é domingo
  startTime: string; // formato "HH:MM"
  endTime: string; // formato "HH:MM"
  preStartMinutes: number; // minutos antes do horário para iniciar o monitoramento
  postEndMinutes: number; // minutos após o horário para continuar monitorando
}

export interface YouTubeMonitoringSettings {
  youtubeChannelId: string;
  youtubeApiKey: string;
  monitoringEnabled: boolean;
  commentProcessingEnabled: boolean;
  llmEnabled: boolean;
  notificationsEnabled: boolean;
  checkIntervalSeconds: number;
  lastCheckedAt?: string;
  currentLiveStreamId?: string;
  scheduleEnabled?: boolean; // Habilitar monitoramento baseado em agenda
  schedules?: YouTubeMonitoringSchedule[]; // Agendas de monitoramento
  respectPlayerState?: boolean; // Respeitar o estado do player para encerramento
}

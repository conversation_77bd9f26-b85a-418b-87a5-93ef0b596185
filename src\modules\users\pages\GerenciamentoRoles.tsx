import { useState } from "react";
import { useUsers } from "@/contexts/UserContext";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Shield, Plus, Users, Lock } from "lucide-react";
import { getPermissionDescription, Permission, Role, SystemPermissions } from "@/types/user";
import { PermissionGate } from "@/components/PermissionGate";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, Alert<PERSON>ialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import PermissionSelector from "../components/PermissionSelector";
import { usePermissions } from "@/hooks/usePermissions";

const GerenciamentoRoles = () => {
  const { roles, users, loading, deleteRole, createRole } = useUsers();
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [creatingRole, setCreatingRole] = useState<Omit<Role, 'id' | 'createdAt' | 'updatedAt'> | null>(null);

  // Contar usuários por role
  const getUserCountByRole = (roleId: string) => {
    return users.filter(user => user.roles.includes(roleId) && user.isActive).length;
  };

  const handleViewDetails = (role: Role) => {
    setSelectedRole(role);
    setIsDetailsOpen(true);
  };

  const handleDeleteRole = async () => {
    if (!roleToDelete) return;

    try {
      await deleteRole(roleToDelete.id);
      setRoleToDelete(null);
    } catch (error) {
      // Toast já é mostrado no contexto
    }
  };

  const handleCreateRole = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!creatingRole) return;

    try {
      await createRole(creatingRole);
      setCreatingRole(null);
    } catch (error) {
      // Toast já é mostrado no contexto
    }
  };

  const handleOpenCreateRoleDialog = async () => {
    setCreatingRole({
      name: "",
      description: "",
      permissions: [] as Permission[],
      isSystemRole: false,
      isActive: true
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Roles e Permissões</h1>
          <p className="text-muted-foreground">
            Gerencie as roles do sistema e suas permissões
          </p>
        </div>

        <PermissionGate permission="roles:manage">
          <Button onClick={handleOpenCreateRoleDialog}>
            <Plus className="mr-2 h-4 w-4" />
            Nova Role
          </Button>
        </PermissionGate>
      </div>

      {/* Estatísticas */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{roles.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Roles Ativas</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(role => role.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usuários Ativos</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {users.filter(user => user.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Roles do Sistema</CardTitle>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(role => role.isSystemRole).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabela de roles */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Descrição</TableHead>
              <TableHead>Tipo</TableHead>
              <TableHead>Usuários</TableHead>
              <TableHead>Permissões</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {roles.map((role) => (
              <TableRow key={role.id}>
                <TableCell className="font-medium">{role.name}</TableCell>
                <TableCell className="max-w-xs truncate">{role.description}</TableCell>
                <TableCell>
                  <Badge variant={role.isSystemRole ? "default" : "secondary"}>
                    {role.isSystemRole ? "Sistema" : "Personalizada"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{getUserCountByRole(role.id)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {role.permissions.length} permissões
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={role.isActive ? "default" : "secondary"}>
                    {role.isActive ? "Ativa" : "Inativa"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewDetails(role)}
                  >
                    Ver Detalhes
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Dialog de detalhes da role */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>{selectedRole?.name}</span>
            </DialogTitle>
            <DialogDescription>
              Detalhes e permissões da role
            </DialogDescription>
          </DialogHeader>

          {selectedRole && (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Informações Gerais</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Descrição:</span>
                    <p>{selectedRole.description}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Usuários com esta role:</span>
                    <p>{getUserCountByRole(selectedRole.id)}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Tipo:</span>
                    <p>{selectedRole.isSystemRole ? "Role do Sistema" : "Role Personalizada"}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Status:</span>
                    <p>{selectedRole.isActive ? "Ativa" : "Inativa"}</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">
                  Permissões ({selectedRole.permissions.length})
                </h4>
                <div className="grid gap-2 max-h-60 overflow-y-auto">
                  {selectedRole.permissions.map((permission) => (
                    <div key={permission.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <code className="text-xs bg-muted px-1 py-0.5 rounded">
                          {permission.id}
                        </code>
                        <p className="text-sm text-muted-foreground mt-1">
                          {getPermissionDescription(permission.id)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <AlertDialog open={!!roleToDelete} onOpenChange={() => setRoleToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a role <strong>{roleToDelete?.name}</strong>?
              Esta ação não pode ser desfeita e todos os usuários com esta role perderão suas permissões.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRole} className="bg-destructive text-destructive-foreground">
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={!!creatingRole} onOpenChange={() => setCreatingRole(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Criar Nova Role</DialogTitle>
            <DialogDescription>
              Preencha os dados da nova role abaixo
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleCreateRole} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Nome da Role
              </label>
              <Input
                id="name"
                value={creatingRole?.name}
                onChange={(e) => setCreatingRole(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Digite o nome da role"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                Descrição
              </label>
              <Textarea
                id="description"
                value={creatingRole?.description}
                onChange={(e) => setCreatingRole(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Digite a descrição da role"
                required
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="permissions" className="text-sm font-medium">
                Permissões
              </label>
              <PermissionSelector
                selectedPermissions={creatingRole?.permissions}
                onChange={(permissions) => setCreatingRole(prev => ({ ...prev, permissions }))}
              />
            </div>
            <Button type="submit" className="w-full">
              Criar Role
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GerenciamentoRoles;

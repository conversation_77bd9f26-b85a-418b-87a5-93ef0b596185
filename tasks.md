# Tarefas do Projeto Belém Connect

## Tarefas Concluídas

- Configuração inicial do projeto com React, TypeScript, Vite e shadcn/ui
- Implementação da autenticação com Firebase
- Criação dos módulos principais (Membros, Pedidos de Oração, Eventos, Configurações)
- Implementação do layout e navegação principal
- Atualização do sistema de pedidos de oração com status avançados (revisão, pendente, lido, descartado, depois)

## Tarefas em Andamento

- [ ] Monitoramento de transmissões ao vivo do YouTube

  - [x] Detecção automática de transmissões ao vivo
  - [x] Atualização da página inicial com o vídeo ao vivo
  - [x] Monitoramento de comentários
  - [x] Processamento de comentários com LLM para identificar pedidos de oração
  - [x] Criação de pedidos de oração a partir de comentários
  - [x] Sistema de notificações para novos pedidos
  - [x] Botão flutuante para controle rápido do monitoramento e processamento
  - [x] Atualização automática do status da transmissão quando encerrada
  - [x] Exibição da última transmissão encerrada na página inicial
  - [x] Opção para respeitar o estado do player para encerramento da transmissão
  - [x] Agendamento automático de monitoramento baseado nos horários dos cultos
  - [x] Melhorias no processamento de comentários e logs detalhados para depuração
  - [x] Correção de bugs na captura e processamento de comentários
  - [x] Correção de bugs no controle de monitoramento e tratamento de erros assíncronos
  - [x] Adição de botão para processamento manual de comentários
  - [x] Adição de botão "Processar Agora" no menu de controles rápidos
  - [x] Tratamento de erro para vídeos com comentários desativados
  - [x] Suporte para captura de mensagens de chat ao vivo em transmissões ativas
  - [ ] Integração com Facebook para monitoramento de comentários

- [x] Integração com provedores de LLM
  - [x] Suporte para OpenAI
  - [x] Suporte para OpenRouter
  - [x] Suporte para Groq
  - [x] Interface para configuração de provedores
  - [x] Seleção de modelos com destaque para recomendados e gratuitos

## Próximas Tarefas

- [ ] Melhorias na interface do usuário
- [ ] Implementação de relatórios e estatísticas
- [ ] Integração com outros serviços de mídia social
- [ ] Implementação de backup automático dos dados

import { useQuery } from "@tanstack/react-query";
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  where,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar, ChevronRight, Home, MapPin } from "lucide-react";
import { Link } from "react-router-dom";
import { YouTubeStream } from "@/components/social/YouTubeStream";
import { InstagramFeed } from "@/components/social/InstagramFeed";
import { Logo } from "@/components/Logo";

const Public = () => {
  const { data: events } = useQuery({
    queryKey: ["public-events"],
    queryFn: async () => {
      const today = new Date().toISOString().split("T")[0];
      const eventsRef = collection(db, "events");
      const q = query(
        eventsRef,
        where("date", ">=", today),
        orderBy("date", "asc"),
        limit(3)
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
    },
  });

  // Removido a consulta manual do status da transmissão ao vivo
  // O componente YouTubeStream agora faz isso automaticamente

  const { data: congregations } = useQuery({
    queryKey: ["public-congregations"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "congregacoes"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
    },
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <div className="relative h-16 flex justify-between items-center">
        <a href="/dashboard" className="absolute top-4 right-4">
          Administrar
        </a>
      </div>
      {/* Hero Section */}
      <section className="relative h-[600px] flex items-center justify-center text-center">
        <div className="absolute inset-0 bg-[url('/church-bg.jpg')] bg-cover bg-center" />
        <div className="absolute inset-0 bg-black/60" />
        <div className="relative z-10 max-w-4xl mx-auto px-4">
          <div className="flex justify-center mb-4">
            <Logo size={20} />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Igreja Evangélica Assembleia de Deus
          </h1>
          <h2 className="text-2xl md:text-4xl font-bold text-white mb-6">
            Mirassol D'Oeste - MT
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Uma igreja que ama você!
          </p>
          <Button asChild size="lg">
            <a href="#eventos" className="gap-2">
              <Calendar className="w-5 h-5" />
              Ver Próximos Eventos
            </a>
          </Button>
        </div>
      </section>

      {/* Transmissão ao Vivo */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <YouTubeStream autoCheck={true} respectPlayerState={true} />
        </div>
      </section>

      {/* Feed do Instagram */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <InstagramFeed />
        </div>
      </section>

      {/* Próximos Eventos */}
      <section id="eventos" className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Próximos Eventos
          </h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {events?.map((event: any) => (
              <Card
                key={event.id}
                className="hover:bg-muted/50 transition-colors"
              >
                <CardHeader>
                  <CardTitle>{event.title}</CardTitle>
                  <CardDescription>
                    {format(new Date(event.date), "PPP", { locale: ptBR })} às{" "}
                    {event.time}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    {event.description}
                  </p>
                  <Button variant="outline" asChild className="w-full">
                    <Link to={`/eventos/${event.id}`} className="gap-2">
                      Ver detalhes
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Nossas Congregações */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Nossas Congregações
          </h2>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {congregations?.map((congregation: any) => (
              <Card key={congregation.id}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    {congregation.nome}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    {congregation.endereco}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-muted py-12">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            © {new Date().getFullYear()} Igreja Evangélica Assembleia de Deus.
            Todos os direitos reservados.
          </p>
        </div>
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            Desenvolvido por <a href="https://wendertech.com.br" target="_blank">WenderTech</a>
          </p>
        </div>
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            {/* Dashboard */}
            <a href="/dashboard" >Administrar</a>
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Public;

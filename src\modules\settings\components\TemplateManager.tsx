import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { collection, addDoc, doc, updateDoc, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Template, TemplateType } from '@/types/template';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export const TemplateManager = () => {
  const { toast } = useToast();
  const [editingTemplate, setEditingTemplate] = useState<Partial<Template>>({
    type: 'prayer',
    active: true,
  });

  // Buscar templates
  const { data: templates, refetch } = useQuery({
    queryKey: ['prayer-templates'],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, 'prayer-templates'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Template[];
    }
  });

  const handleSave = async () => {
    try {
      if (!editingTemplate.name || !editingTemplate.template) {
        toast({
          title: "Erro",
          description: "Preencha todos os campos obrigatórios",
          variant: "destructive",
        });
        return;
      }

      const templateData = {
        ...editingTemplate,
        updatedAt: new Date().toISOString(),
        createdAt: editingTemplate.id ? editingTemplate.createdAt : new Date().toISOString(),
      };

      if (editingTemplate.id) {
        await updateDoc(doc(db, 'prayer-templates', editingTemplate.id), templateData);
      } else {
        await addDoc(collection(db, 'prayer-templates'), templateData);
      }

      toast({
        title: "Sucesso",
        description: "Template salvo com sucesso",
      });

      setEditingTemplate({
        type: 'prayer',
        active: true,
      });
      refetch();
    } catch (error) {
      console.error('Erro ao salvar template:', error);
      toast({
        title: "Erro",
        description: "Erro ao salvar template",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Gerenciar Templates</CardTitle>
          <CardDescription>
            Crie e edite templates para pedidos de oração e avisos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Nome</label>
                <Input
                  value={editingTemplate.name || ''}
                  onChange={(e) => setEditingTemplate(prev => ({
                    ...prev,
                    name: e.target.value
                  }))}
                  placeholder="Nome do template"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Tipo</label>
                <Select
                  value={editingTemplate.type}
                  onValueChange={(value: TemplateType) => setEditingTemplate(prev => ({
                    ...prev,
                    type: value
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="prayer">Pedido de Oração</SelectItem>
                    <SelectItem value="announcement">Aviso</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Descrição</label>
              <Input
                value={editingTemplate.description || ''}
                onChange={(e) => setEditingTemplate(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
                placeholder="Descrição do template"
              />
            </div>

            <div>
              <label className="text-sm font-medium">Template</label>
              <Textarea
                value={editingTemplate.template || ''}
                onChange={(e) => setEditingTemplate(prev => ({
                  ...prev,
                  template: e.target.value
                }))}
                placeholder="Use {variavel} para placeholders"
                rows={5}
              />
              <p className="text-sm text-gray-500 mt-1">
                Variáveis disponíveis: {'{nome}'}, {'{pedido}'}, {'{data}'}
              </p>
            </div>

            <Button onClick={handleSave}>
              {editingTemplate.id ? 'Atualizar' : 'Criar'} Template
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {templates?.map((template) => (
          <Card key={template.id}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {template.name}
                <Badge variant={template.active ? 'default' : 'secondary'}>
                  {template.active ? 'Ativo' : 'Inativo'}
                </Badge>
              </CardTitle>
              <CardDescription>{template.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-secondary p-2 rounded-md text-sm">
                {template.template}
              </pre>
              <div className="mt-4 flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setEditingTemplate(template)}
                >
                  Editar
                </Button>
                <Button
                  variant="outline"
                  onClick={async () => {
                    await updateDoc(doc(db, 'prayer-templates', template.id), {
                      active: !template.active,
                      updatedAt: new Date().toISOString(),
                    });
                    refetch();
                  }}
                >
                  {template.active ? 'Desativar' : 'Ativar'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
# Especificações Técnicas - Sistema de Usuários

## Arquitetura de Segurança

### Camadas de Proteção

1. **Autenticação (Firebase Auth)**
   - Verificação de identidade
   - Gerenciamento de sessões
   - Tokens JWT

2. **Autorização (Custom)**
   - Verificação de roles
   - Verificação de permissões
   - Controle de acesso granular

3. **Validação de Dados**
   - Sanitização de entrada
   - Validação de formato
   - Verificação de integridade

4. **Auditoria e Monitoramento**
   - Log de operações
   - Detecção de atividades suspeitas
   - Rate limiting

### Fluxo de Autenticação

```mermaid
graph TD
    A[Login] --> B[Firebase Auth]
    B --> C{Usuário Válido?}
    C -->|Não| D[Erro de Login]
    C -->|Sim| E[Buscar Perfil Firestore]
    E --> F{Perfil Existe?}
    F -->|Não| G[Criar Perfil Padrão]
    F -->|Sim| H[Carregar Permissões]
    G --> H
    H --> I[Usuário Autenticado]
```

### Fluxo de Autorização

```mermaid
graph TD
    A[Ação do Usuário] --> B[Verificar Autenticação]
    B --> C{Autenticado?}
    C -->|Não| D[Redirecionar Login]
    C -->|Sim| E[Verificar Permissões]
    E --> F{Tem Permissão?}
    F -->|Não| G[Acesso Negado]
    F -->|Sim| H[Executar Ação]
    H --> I[Log de Auditoria]
```

## Estrutura de Dados Detalhada

### Coleção `users`

```typescript
interface User {
  id: string;                    // Firebase Auth UID
  email: string;                 // Email único
  displayName: string;           // Nome para exibição
  roles: string[];              // Array de IDs de roles
  isActive: boolean;            // Status ativo/inativo
  createdAt: number;            // Timestamp de criação
  updatedAt: number;            // Timestamp de última atualização
  createdBy: string;            // UID do usuário que criou
  lastLoginAt?: number;         // Timestamp do último login
  profilePicture?: string;      // URL da foto de perfil
}
```

### Coleção `roles`

```typescript
interface Role {
  id: string;                   // ID único da role
  name: string;                 // Nome da role
  description: string;          // Descrição da role
  permissions: Permission[];    // Array de permissões
  isActive: boolean;           // Status ativo/inativo
  createdAt: number;           // Timestamp de criação
  updatedAt: number;           // Timestamp de última atualização
  isSystemRole: boolean;       // Se é role do sistema (não pode ser deletada)
}
```

### Interface `Permission`

```typescript
interface Permission {
  id: string;                   // ID da permissão (ex: "users:create")
  name: string;                 // Nome legível
  description: string;          // Descrição da permissão
  resource: string;             // Recurso (ex: "users")
  action: string;               // Ação (ex: "create")
}
```

## APIs e Funções

### Funções de Usuário (`src/lib/users.ts`)

#### `createUser(userData, currentUserId)`
- **Propósito**: Criar novo usuário
- **Validações**: Email único, senha forte, permissões
- **Segurança**: Rate limiting, sanitização, auditoria

#### `updateUser(userId, userData, currentUserId)`
- **Propósito**: Atualizar usuário existente
- **Validações**: Permissões de modificação, dados válidos
- **Segurança**: Verificação de autorização, auditoria

#### `getUserById(userId)`
- **Propósito**: Buscar usuário por ID
- **Cache**: Implementar cache se necessário
- **Segurança**: Verificação de permissões de leitura

#### `getAllUsers()`
- **Propósito**: Listar todos os usuários
- **Filtros**: Apenas usuários ativos por padrão
- **Paginação**: Implementar se necessário

### Funções de Segurança (`src/lib/security.ts`)

#### `sanitizeInput(input)`
- Remove caracteres HTML
- Limita tamanho
- Normaliza espaços

#### `validateUserData(data)`
- Valida formato de email
- Verifica comprimento de campos
- Valida roles

#### `isStrongPassword(password)`
- Verifica comprimento mínimo
- Verifica complexidade
- Retorna erros específicos

#### `canUserModifyUser(currentUser, targetUser, action)`
- Verifica hierarquia de roles
- Previne auto-modificação destrutiva
- Implementa regras de negócio

## Hooks e Contextos

### `useAuth()`
- Estado de autenticação
- Informações do usuário atual
- Funções de login/logout
- Permissões calculadas

### `useUsers()`
- Lista de usuários
- Operações CRUD
- Estado de carregamento
- Tratamento de erros

### `usePermissions()`
- Verificações de permissão
- Verificações de role
- Funções utilitárias
- Cache de permissões

## Componentes de Segurança

### `ProtectedRoute`
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: string;
  fallbackPath?: string;
}
```

### `PermissionGate`
```typescript
interface PermissionGateProps {
  children: React.ReactNode;
  permission?: string;
  role?: string;
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}
```

## Configurações de Segurança

### Constantes (`src/lib/security.ts`)

```typescript
export const SECURITY_CONSTANTS = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION_MS: 15 * 60 * 1000,
  PASSWORD_MIN_LENGTH: 8,
  SESSION_TIMEOUT_MS: 24 * 60 * 60 * 1000,
  MAX_USERS_PER_BATCH: 10,
  MAX_ROLE_ASSIGNMENTS: 5
};
```

### Regras do Firestore

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Regras para coleção users
    match /users/{userId} {
      allow read: if request.auth != null && 
        (request.auth.uid == userId || hasPermission('users:read'));
      allow write: if request.auth != null && 
        hasPermission('users:update');
      allow create: if request.auth != null && 
        hasPermission('users:create');
      allow delete: if request.auth != null && 
        hasPermission('users:delete');
    }
    
    // Regras para coleção roles
    match /roles/{roleId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        hasPermission('roles:manage');
    }
    
    // Função auxiliar para verificar permissões
    function hasPermission(permission) {
      let userDoc = get(/databases/$(database)/documents/users/$(request.auth.uid));
      let userRoles = userDoc.data.roles;
      // Lógica para verificar se alguma role do usuário tem a permissão
      return true; // Simplificado
    }
  }
}
```

## Performance e Otimização

### Estratégias de Cache

1. **Cache de Permissões**
   - Armazenar permissões calculadas no contexto
   - Invalidar quando roles mudam
   - TTL de 5 minutos

2. **Cache de Usuários**
   - Lista de usuários em memória
   - Atualização incremental
   - Paginação para listas grandes

3. **Cache de Roles**
   - Roles raramente mudam
   - Cache de longa duração
   - Invalidação manual

### Otimizações de Query

1. **Índices Firestore**
   - `users` por `isActive` e `createdAt`
   - `users` por `roles` (array-contains)
   - `roles` por `isActive` e `name`

2. **Queries Compostas**
   - Filtrar usuários ativos
   - Ordenar por data de criação
   - Limitar resultados

## Monitoramento e Métricas

### Eventos de Auditoria

```typescript
interface SecurityEvent {
  timestamp: string;
  userId: string;
  action: string;
  details: Record<string, any>;
  success: boolean;
  userAgent: string;
  ipAddress?: string;
}
```

### Métricas Importantes

1. **Autenticação**
   - Taxa de sucesso de login
   - Tentativas de login falhadas
   - Tempo de sessão médio

2. **Autorização**
   - Tentativas de acesso negado
   - Uso de permissões por recurso
   - Distribuição de roles

3. **Operações de Usuário**
   - Criações de usuário por dia
   - Modificações de usuário
   - Usuários ativos vs inativos

### Alertas de Segurança

1. **Atividade Suspeita**
   - Múltiplas tentativas de login falhadas
   - Tentativas de acesso a recursos não autorizados
   - Modificações em massa de usuários

2. **Anomalias**
   - Login de localizações incomuns
   - Atividade fora do horário normal
   - Padrões de uso anômalos

## Backup e Recuperação

### Estratégia de Backup

1. **Backup Automático**
   - Firestore Export diário
   - Backup de configurações
   - Versionamento de dados

2. **Backup Manual**
   - Antes de mudanças críticas
   - Backup de roles e permissões
   - Exportação de usuários

### Plano de Recuperação

1. **Recuperação de Usuário**
   - Restaurar perfil do backup
   - Recriar no Firebase Auth se necessário
   - Validar integridade dos dados

2. **Recuperação de Sistema**
   - Restaurar roles padrão
   - Recriar usuário admin
   - Validar permissões

## Testes

### Testes Unitários

- Funções de validação
- Cálculo de permissões
- Sanitização de dados

### Testes de Integração

- Fluxo completo de autenticação
- Operações CRUD de usuários
- Verificação de permissões

### Testes de Segurança

- Tentativas de bypass de autorização
- Injeção de dados maliciosos
- Rate limiting

### Testes de Performance

- Tempo de carregamento de permissões
- Performance de queries
- Stress test de criação de usuários

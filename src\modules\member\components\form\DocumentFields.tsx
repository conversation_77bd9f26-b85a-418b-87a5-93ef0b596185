import { Input } from "@/components/ui/input";

interface DocumentFieldsProps {
  register: any;
}

export const DocumentFields = ({ register }: DocumentFieldsProps) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label htmlFor="rg" className="text-sm font-medium">
          RG
        </label>
        <Input
          id="rg"
          {...register("rg", { required: true })}
          placeholder="Digite o RG"
          className="transition-all focus:scale-[1.01]"
        />
      </div>

      <div className="space-y-2">
        <label htmlFor="cpf" className="text-sm font-medium">
          CPF
        </label>
        <Input
          id="cpf"
          {...register("cpf", { required: true })}
          placeholder="Digite o CPF"
          className="transition-all focus:scale-[1.01]"
        />
      </div>
    </div>
  );
};
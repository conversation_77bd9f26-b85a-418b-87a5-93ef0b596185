import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PedidoOracao, PedidoStatus } from "@/types/prayer";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  ArrowLeft,
  ArrowRight,
  Check,
  Trash,
  Clock,
  RefreshCw,
  AlertCircle,
} from "lucide-react";
import { useState } from "react";
import { ConfirmationDialog } from "../ConfirmationDialog";
import { LeftCardButton } from "./LeftCardButton";
import { RightCardButton } from "./RightCardButton";

interface PrayerCardProps {
  pedido: PedidoOracao;
  currentIndex: number;
  totalPedidos: number;
  onPrevious: () => void;
  onNext: () => void;
  onMarkAsRead: (id: string) => Promise<void>;
  onDiscard: (id: string) => Promise<void>;
  onMarkForLater?: (id: string) => Promise<void>;
  onMarkForReview?: (id: string) => Promise<void>;
  onMarkAsPending?: (id: string) => Promise<void>;
}

export const PrayerCard = ({
  pedido,
  currentIndex,
  totalPedidos,
  onPrevious,
  onNext,
  onMarkAsRead,
  onDiscard,
  onMarkForLater,
  onMarkForReview,
  onMarkAsPending,
}: PrayerCardProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Componente para exibir o status do pedido
  const StatusBadge = ({ status }: { status: PedidoStatus }) => {
    const statusConfig = {
      pendente: { label: "Pendente", variant: "outline" as const },
      revisao: { label: "Em Revisão", variant: "secondary" as const },
      lido: { label: "Lido", variant: "default" as const },
      descartado: { label: "Descartado", variant: "destructive" as const },
      depois: { label: "Para Depois", variant: "warning" as const },
    };

    const config = statusConfig[status] || statusConfig.pendente;

    return (
      <Badge variant={config.variant} className="ml-2">
        {config.label}
      </Badge>
    );
  };

  const tipo = pedido?.tipo === "aviso" ? "Aviso" : "Pedido de Oração";
  return (
    <div className="bg-card rounded-lg shadow-lg">
      <div className="relative flex items-center mb-4 p-6">
        {/* Botão de navegação à esquerda */}
        <LeftCardButton onClick={onPrevious} disabled={currentIndex == 0}>
          <ArrowLeft className="text-white h-8 w-8" />
        </LeftCardButton>
        {/* Conteúdo principal do card */}
        <div className="flex flex-col justify-between gap-2 mx-auto">
          <div className="text-muted-foreground flex flex-wrap gap-2 mb-4 justify-between">
            <div className="text-xl mb-4 flex items-center">
              {tipo} {currentIndex + 1} de {totalPedidos}
              <StatusBadge status={pedido.status} />
            </div>
            <div className="text-xl mb-4">
              {format(new Date(pedido.createdAt), "EEEE',' PPP", {
                locale: ptBR,
              })}
            </div>
          </div>

          <div className="gap-2 mb-4 justify-between">
            <div className="text-muted-foreground text-xl mb-4">Nome</div>
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              {pedido.nome}
            </h2>
          </div>

          <div className="gap-2 mb-4 justify-between">
            <div className="text-muted-foreground text-xl mb-4">{tipo}</div>
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              {pedido.pedido}
            </h2>
          </div>

          <div className="gap-2 mb-4 justify-between">
            <div className="text-muted-foreground text-xl mb-4">Origem</div>
            <h3 className="text-3xl sm:text-4xl font-bold mb-6">
              {pedido.origem || "Portaria"}
            </h3>
          </div>

          <div className="flex flex-wrap gap-2 justify-center">
            {/* Botão para marcar como lido */}
            <Button
              onClick={() => onMarkAsRead(pedido.id)}
              className="flex-1"
              variant={pedido.status === "lido" ? "default" : "outline"}
            >
              <Check className="mr-2" />
              Lido
            </Button>

            {/* Botão para marcar para depois */}
            {onMarkForLater && (
              <Button
                onClick={() => onMarkForLater(pedido.id)}
                className="flex-1"
                variant={pedido.status === "depois" ? "default" : "outline"}
              >
                <Clock className="mr-2" />
                Depois
              </Button>
            )}

            {/* Botão para marcar para revisão */}
            {onMarkForReview && (
              <Button
                onClick={() => onMarkForReview(pedido.id)}
                className="flex-1"
                variant={pedido.status === "revisao" ? "default" : "outline"}
              >
                <RefreshCw className="mr-2" />
                Revisão
              </Button>
            )}

            {/* Botão para marcar como pendente */}
            {onMarkAsPending && (
              <Button
                onClick={() => onMarkAsPending(pedido.id)}
                className="flex-1"
                variant={pedido.status === "pendente" ? "default" : "outline"}
              >
                <AlertCircle className="mr-2" />
                Pendente
              </Button>
            )}

            {/* Botão para descartar */}
            <Button
              onClick={() => setIsOpen(true)}
              className="flex-1"
              variant={
                pedido.status === "descartado" ? "default" : "destructive"
              }
            >
              <Trash className="mr-2" />
              Descartar
            </Button>
          </div>
        </div>

        {/* Botão de navegação à direita */}
        <RightCardButton
          onClick={onNext}
          disabled={currentIndex === totalPedidos - 1}
        >
          <ArrowRight className="text-white h-8 w-8" />
        </RightCardButton>
      </div>

      <ConfirmationDialog
        title="Descartar"
        description="Tem certeza que deseja descartar este pedido?"
        isOpen={isOpen}
        onConfirm={async () => {
          await onDiscard(pedido.id);
          setIsOpen(false);
        }}
        onClose={() => setIsOpen(false)}
      />
    </div>
  );
};

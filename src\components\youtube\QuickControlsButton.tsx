import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { startAllMonitoring, stopAllMonitoring } from "@/lib/monitoring";
import {
  fetchLiveStreamComments,
  processComments,
  getActiveLiveStream,
} from "@/lib/youtube";
import { Video, MessageSquare, X, Settings } from "lucide-react";
import { cn } from "@/lib/utils";

interface QuickControlsButtonProps {
  initialMonitoringEnabled?: boolean;
  initialCommentProcessingEnabled?: boolean;
}

export const QuickControlsButton = ({
  initialMonitoringEnabled = false,
  initialCommentProcessingEnabled = false,
}: QuickControlsButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [monitoringEnabled, setMonitoringEnabled] = useState(
    initialMonitoringEnabled
  );
  const [commentProcessingEnabled, setCommentProcessingEnabled] = useState(
    initialCommentProcessingEnabled
  );
  const { toast } = useToast();

  const toggleMonitoring = async () => {
    // Evitar múltiplos cliques
    if (isLoading) return;

    setIsLoading(true);

    try {
      console.log("Iniciando toggle de monitoramento...");
      // Armazenar o estado atual para comparação
      const currentState = monitoringEnabled;
      const newMonitoringState = !currentState;

      console.log(
        `Alterando estado de monitoramento: ${currentState} -> ${newMonitoringState}`
      );

      // Atualizar estado local primeiro para feedback imediato ao usuário
      setMonitoringEnabled(newMonitoringState);

      // Obter configurações atuais
      console.log("Obtendo configurações do Firestore...");
      const docRef = doc(db, "system", "settings");
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        throw new Error("Configurações não encontradas");
      }

      // Atualizar configurações no Firestore
      console.log(
        `Atualizando configurações no Firestore para monitoringEnabled=${newMonitoringState}`
      );
      await updateDoc(docRef, {
        "youtubeMonitoring.monitoringEnabled": newMonitoringState,
      });

      // Iniciar ou parar o monitoramento
      if (newMonitoringState) {
        console.log("Iniciando monitoramento...");
        // Usar Promise.resolve para garantir que o código continue mesmo se houver um erro
        const monitoringPromise = Promise.resolve().then(() =>
          startAllMonitoring()
        );

        // Aguardar no máximo 5 segundos para evitar bloqueio indefinido
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(
            () => reject(new Error("Timeout ao iniciar monitoramento")),
            5000
          );
        });

        try {
          await Promise.race([monitoringPromise, timeoutPromise]);
          console.log("Monitoramento iniciado com sucesso");
        } catch (monitoringError) {
          console.error(
            "Erro ou timeout ao iniciar monitoramento:",
            monitoringError
          );
          // Continuar mesmo com erro, para não bloquear a UI
        }

        toast({
          title: "Monitoramento ativado",
          description:
            "O sistema está monitorando transmissões ao vivo do YouTube.",
        });
      } else {
        console.log("Parando monitoramento...");
        try {
          stopAllMonitoring();
          console.log("Monitoramento parado com sucesso");
        } catch (stopError) {
          console.error("Erro ao parar monitoramento:", stopError);
          // Continuar mesmo com erro
        }

        toast({
          title: "Monitoramento desativado",
          description: "O sistema parou de monitorar transmissões do YouTube.",
        });
      }

      console.log("Toggle de monitoramento concluído com sucesso");
    } catch (error) {
      console.error("Erro ao alternar monitoramento:", error);

      // Reverter o estado local em caso de erro
      setMonitoringEnabled(monitoringEnabled);

      toast({
        variant: "destructive",
        title: "Erro ao alternar monitoramento",
        description: "Não foi possível atualizar as configurações.",
      });
    } finally {
      // Garantir que o estado de carregamento seja atualizado mesmo em caso de erro
      setIsLoading(false);
    }
  };

  const toggleCommentProcessing = async () => {
    // Evitar múltiplos cliques
    if (isLoading) return;

    setIsLoading(true);

    try {
      console.log("Iniciando toggle de processamento de comentários...");
      // Armazenar o estado atual para comparação
      const currentState = commentProcessingEnabled;
      const newProcessingState = !currentState;

      console.log(
        `Alterando estado de processamento: ${currentState} -> ${newProcessingState}`
      );

      // Atualizar estado local primeiro para feedback imediato ao usuário
      setCommentProcessingEnabled(newProcessingState);

      // Obter configurações atuais
      console.log("Obtendo configurações do Firestore...");
      const docRef = doc(db, "system", "settings");
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        throw new Error("Configurações não encontradas");
      }

      // Atualizar configurações no Firestore
      console.log(
        `Atualizando configurações no Firestore para commentProcessingEnabled=${newProcessingState}`
      );
      await updateDoc(docRef, {
        "youtubeMonitoring.commentProcessingEnabled": newProcessingState,
      });

      toast({
        title: `Processamento de comentários ${newProcessingState ? "ativado" : "desativado"
          }`,
        description: `O sistema ${newProcessingState ? "começou a" : "parou de"
          } processar comentários do YouTube.`,
      });

      console.log(
        "Toggle de processamento de comentários concluído com sucesso"
      );
    } catch (error) {
      console.error("Erro ao alternar processamento de comentários:", error);

      // Reverter o estado local em caso de erro
      setCommentProcessingEnabled(commentProcessingEnabled);

      toast({
        variant: "destructive",
        title: "Erro ao alternar processamento",
        description: "Não foi possível atualizar as configurações.",
      });
    } finally {
      // Garantir que o estado de carregamento seja atualizado mesmo em caso de erro
      setIsLoading(false);
    }
  };

  // Função para processar comentários manualmente
  const processCommentsNow = async () => {
    // Evitar múltiplos cliques
    if (isLoading) return;

    setIsLoading(true);

    try {
      console.log("Iniciando processamento manual de comentários...");

      // Buscar a transmissão ao vivo ativa
      const liveStream = await getActiveLiveStream();

      if (!liveStream) {
        console.log("Nenhuma transmissão ao vivo ativa encontrada");
        toast({
          variant: "destructive",
          title: "Nenhuma transmissão ativa",
          description:
            "Não há transmissão ao vivo ativa para processar comentários.",
        });
        return;
      }

      console.log(
        `Transmissão ao vivo encontrada: ${liveStream.title} (ID: ${liveStream.videoId})`
      );

      // Buscar comentários
      console.log(
        `Buscando comentários para a transmissão ${liveStream.videoId}...`
      );

      try {
        const comments = await fetchLiveStreamComments(liveStream.videoId);

        if (comments.length === 0) {
          console.log("Nenhum comentário encontrado para processar");
          toast({
            title: "Nenhum comentário encontrado",
            description: "Não foram encontrados comentários para processar.",
          });
          return;
        }

        console.log(
          `${comments.length} comentários encontrados, processando...`
        );

        // Processar comentários
        await processComments(comments);

        console.log(
          "Processamento manual de comentários concluído com sucesso"
        );

        toast({
          title: "Comentários processados",
          description: `${comments.length} comentários foram processados com sucesso.`,
        });
      } catch (commentError) {
        if (commentError.message === "COMMENTS_DISABLED") {
          console.log("Comentários estão desativados para este vídeo");
          toast({
            variant: "destructive",
            title: "Comentários desativados",
            description:
              "Os comentários estão desativados para este vídeo. Não é possível processar comentários.",
          });
          return;
        }
        throw commentError; // Propagar outros erros para serem tratados no catch externo
      }

      // Estas linhas foram movidas para dentro do bloco try acima
    } catch (error) {
      console.error("Erro ao processar comentários manualmente:", error);

      toast({
        variant: "destructive",
        title: "Erro ao processar comentários",
        description:
          "Ocorreu um erro ao processar os comentários. Verifique o console para mais detalhes.",
      });
    } finally {
      // Garantir que o estado de carregamento seja atualizado mesmo em caso de erro
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end space-y-2">
      {/* Menu de controles rápidos */}
      <div
        className={cn(
          "flex flex-col items-end space-y-2 transition-all duration-200",
          isOpen
            ? "opacity-100 scale-100"
            : "opacity-0 scale-95 pointer-events-none"
        )}
      >
        {/* Botão de monitoramento */}
        <Button
          variant={monitoringEnabled ? "default" : "outline"}
          size="sm"
          className="flex items-center gap-2 shadow-lg"
          onClick={toggleMonitoring}
          disabled={isLoading}
        >
          <Video className="h-4 w-4" />
          {monitoringEnabled
            ? "Desativar Monitoramento"
            : "Ativar Monitoramento"}
        </Button>

        {/* Botão de processamento de comentários */}
        <Button
          variant={commentProcessingEnabled ? "default" : "outline"}
          size="sm"
          className="flex items-center gap-2 shadow-lg"
          onClick={toggleCommentProcessing}
          disabled={isLoading || !monitoringEnabled}
        >
          <MessageSquare className="h-4 w-4" />
          {commentProcessingEnabled
            ? "Parar Processamento"
            : "Iniciar Processamento"}
        </Button>

        {/* Botão para processar comentários manualmente */}
        <Button
          variant="secondary"
          size="sm"
          className="flex items-center gap-2 shadow-lg"
          onClick={processCommentsNow}
          disabled={isLoading || !monitoringEnabled}
        >
          <MessageSquare className="h-4 w-4" />
          Processar Agora
        </Button>

        {/* Botão para ir para configurações completas */}
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 shadow-lg"
          onClick={() => (window.location.href = "/dashboard/settings")}
        >
          <Settings className="h-4 w-4" />
          Configurações Completas
        </Button>
      </div>

      {/* Botão principal para abrir/fechar o menu */}
      <Button
        variant="default"
        size="icon"
        className="h-12 w-12 rounded-full shadow-lg"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-6 w-6" /> : <Video className="h-6 w-6" />}
      </Button>
    </div>
  );
};

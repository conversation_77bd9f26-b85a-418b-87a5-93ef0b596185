import { LLMConfig, <PERSON><PERSON><PERSON><PERSON>, LLMResponse, PrayerRequestAnalysis } from "@/types/llm";
import { doc, getDoc } from "firebase/firestore";
import { db } from "./firebase";

// Função para obter as configurações de LLM do Firestore
export const getLLMConfig = async (): Promise<LLMConfig | null> => {
  try {
    const docRef = doc(db, "system", "settings");
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists() && docSnap.data().llm) {
      return docSnap.data().llm as LLMConfig;
    }
    
    return null;
  } catch (error) {
    console.error("Erro ao obter configurações de LLM:", error);
    return null;
  }
};

// Função para fazer uma requisição ao OpenAI
const callOpenAI = async (
  apiKey: string,
  model: string,
  prompt: string,
  temperature: number = 0.7,
  maxTokens: number = 500
): Promise<LLMResponse> => {
  try {
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: "system", content: "Você é um assistente especializado em analisar comentários e identificar pedidos de oração." },
          { role: "user", content: prompt }
        ],
        temperature,
        max_tokens: maxTokens
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Erro na API OpenAI: ${errorData.error?.message || response.statusText}`);
    }
    
    const data = await response.json();
    return {
      success: true,
      content: data.choices[0].message.content
    };
  } catch (error) {
    console.error("Erro ao chamar OpenAI:", error);
    return {
      success: false,
      content: "",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    };
  }
};

// Função para fazer uma requisição ao OpenRouter
const callOpenRouter = async (
  apiKey: string,
  model: string,
  prompt: string,
  temperature: number = 0.7,
  maxTokens: number = 500
): Promise<LLMResponse> => {
  try {
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`,
        "HTTP-Referer": window.location.origin,
        "X-Title": "Belém Connect"
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: "system", content: "Você é um assistente especializado em analisar comentários e identificar pedidos de oração." },
          { role: "user", content: prompt }
        ],
        temperature,
        max_tokens: maxTokens
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Erro na API OpenRouter: ${errorData.error?.message || response.statusText}`);
    }
    
    const data = await response.json();
    return {
      success: true,
      content: data.choices[0].message.content
    };
  } catch (error) {
    console.error("Erro ao chamar OpenRouter:", error);
    return {
      success: false,
      content: "",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    };
  }
};

// Função para fazer uma requisição ao Groq
const callGroq = async (
  apiKey: string,
  model: string,
  prompt: string,
  temperature: number = 0.7,
  maxTokens: number = 500
): Promise<LLMResponse> => {
  try {
    const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: "system", content: "Você é um assistente especializado em analisar comentários e identificar pedidos de oração." },
          { role: "user", content: prompt }
        ],
        temperature,
        max_tokens: maxTokens
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Erro na API Groq: ${errorData.error?.message || response.statusText}`);
    }
    
    const data = await response.json();
    return {
      success: true,
      content: data.choices[0].message.content
    };
  } catch (error) {
    console.error("Erro ao chamar Groq:", error);
    return {
      success: false,
      content: "",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    };
  }
};

// Função para chamar o LLM configurado
export const callLLM = async (prompt: string): Promise<LLMResponse> => {
  try {
    const config = await getLLMConfig();
    
    if (!config || !config.enabled) {
      return {
        success: false,
        content: "",
        error: "LLM não configurado ou desativado"
      };
    }
    
    const temperature = config.temperature || 0.7;
    const maxTokens = config.maxTokens || 500;
    
    switch (config.provider) {
      case "openai":
        return callOpenAI(config.apiKey, config.model, prompt, temperature, maxTokens);
      case "openrouter":
        return callOpenRouter(config.apiKey, config.model, prompt, temperature, maxTokens);
      case "groq":
        return callGroq(config.apiKey, config.model, prompt, temperature, maxTokens);
      default:
        return {
          success: false,
          content: "",
          error: `Provedor não suportado: ${config.provider}`
        };
    }
  } catch (error) {
    console.error("Erro ao chamar LLM:", error);
    return {
      success: false,
      content: "",
      error: error instanceof Error ? error.message : "Erro desconhecido"
    };
  }
};

// Função para analisar um comentário e verificar se é um pedido de oração
export const analyzePrayerRequest = async (comment: string): Promise<PrayerRequestAnalysis> => {
  try {
    const prompt = `
Analise o seguinte comentário e determine se é um pedido de oração:

"${comment}"

Responda em formato JSON com os seguintes campos:
- isPrayerRequest: boolean (true se for um pedido de oração, false caso contrário)
- confidence: number (0-1, sua confiança na classificação)
- category: string (opcional, categoria do pedido, ex: "saúde", "família", "financeiro")
- extractedRequest: string (opcional, o pedido de oração extraído e reformulado de forma clara)
- tags: array de strings (opcional, palavras-chave relacionadas ao pedido)

Exemplo de resposta:
{
  "isPrayerRequest": true,
  "confidence": 0.95,
  "category": "saúde",
  "extractedRequest": "Oração pela recuperação da minha mãe que está hospitalizada",
  "tags": ["saúde", "hospital", "mãe", "recuperação"]
}
`;

    const response = await callLLM(prompt);
    
    if (!response.success) {
      console.error("Erro na análise do pedido de oração:", response.error);
      // Fallback para o método simples
      const isPrayerRequest = isPotentialPrayerRequestSimple(comment);
      return {
        isPrayerRequest,
        confidence: isPrayerRequest ? 0.6 : 0.4
      };
    }
    
    try {
      // Tentar fazer parse do JSON retornado pelo LLM
      const analysis = JSON.parse(response.content) as PrayerRequestAnalysis;
      return analysis;
    } catch (parseError) {
      console.error("Erro ao fazer parse da resposta do LLM:", parseError);
      // Se não conseguir fazer parse, tentar extrair manualmente
      const isPrayerRequest = response.content.toLowerCase().includes("true");
      return {
        isPrayerRequest,
        confidence: 0.5
      };
    }
  } catch (error) {
    console.error("Erro ao analisar pedido de oração:", error);
    // Fallback para o método simples
    const isPrayerRequest = isPotentialPrayerRequestSimple(comment);
    return {
      isPrayerRequest,
      confidence: isPrayerRequest ? 0.6 : 0.4
    };
  }
};

// Método simples para fallback
const isPotentialPrayerRequestSimple = (text: string): boolean => {
  const prayerKeywords = [
    "oração", "ore", "orar", "orando", "reza", "rezar", "prece", "preces",
    "ajuda", "ajude", "socorro", "doente", "doença", "hospital", "internado",
    "cirurgia", "operação", "saúde", "problema", "dificuldade", "deus", "jesus",
    "senhor", "espírito", "fé", "graça", "milagre", "bênção", "benção"
  ];
  
  const lowerText = text.toLowerCase();
  return prayerKeywords.some(keyword => lowerText.includes(keyword));
};

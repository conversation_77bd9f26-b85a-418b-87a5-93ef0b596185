import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { But<PERSON> } from "@/components/ui/button";
import { Check, Clock, RefreshCw, AlertCircle, Trash } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PedidoOracao, PedidoStatus } from "@/types/prayer";
import { Badge } from "@/components/ui/badge";

interface PrayerListProps {
  pedidos: PedidoOracao[];
  onMarkAsRead: (id: string) => Promise<void>;
  onDiscard?: (id: string) => Promise<void>;
  onMarkForLater?: (id: string) => Promise<void>;
  onMarkForReview?: (id: string) => Promise<void>;
  onMarkAsPending?: (id: string) => Promise<void>;
}

export const PrayerList = ({
  pedidos,
  onMarkAsRead,
  onDiscard,
  onMarkForLater,
  onMarkForReview,
  onMarkAsPending,
}: PrayerListProps) => {
  // Componente para exibir o status do pedido
  const StatusBadge = ({ status }: { status: PedidoStatus }) => {
    const statusConfig = {
      pendente: { label: "Pendente", variant: "outline" as const },
      revisao: { label: "Em Revisão", variant: "secondary" as const },
      lido: { label: "Lido", variant: "default" as const },
      descartado: { label: "Descartado", variant: "destructive" as const },
      depois: { label: "Para Depois", variant: "warning" as const },
    };

    const config = statusConfig[status] || statusConfig.pendente;

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };
  return (
    <div className="rounded-lg shadow-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nome</TableHead>
            <TableHead>Tipo</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Tags</TableHead>
            <TableHead>Data</TableHead>
            <TableHead>Pedido</TableHead>
            <TableHead>Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {pedidos.map((pedido) => (
            <TableRow key={pedido.id}>
              <TableCell className="font-medium">{pedido.nome}</TableCell>
              <TableCell>
                {pedido.tipo === "pedido" ? "Pedido" : "Aviso"}
              </TableCell>
              <TableCell>
                <StatusBadge status={pedido.status} />
              </TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {pedido.tags?.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>
                {format(new Date(pedido.createdAt), "dd/MM/yyyy HH:mm")}
              </TableCell>
              <TableCell className="max-w-md truncate">
                {pedido.pedido}
              </TableCell>
              <TableCell>
                <div className="flex space-x-1">
                  {/* Botão para marcar como lido */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onMarkAsRead(pedido.id)}
                    title="Marcar como lido"
                  >
                    <Check className="h-4 w-4" />
                  </Button>

                  {/* Botão para marcar para depois */}
                  {onMarkForLater && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onMarkForLater(pedido.id)}
                      title="Marcar para depois"
                    >
                      <Clock className="h-4 w-4" />
                    </Button>
                  )}

                  {/* Botão para marcar para revisão */}
                  {onMarkForReview && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onMarkForReview(pedido.id)}
                      title="Marcar para revisão"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  )}

                  {/* Botão para marcar como pendente */}
                  {onMarkAsPending && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onMarkAsPending(pedido.id)}
                      title="Marcar como pendente"
                    >
                      <AlertCircle className="h-4 w-4" />
                    </Button>
                  )}

                  {/* Botão para descartar */}
                  {onDiscard && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDiscard(pedido.id)}
                      title="Descartar"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

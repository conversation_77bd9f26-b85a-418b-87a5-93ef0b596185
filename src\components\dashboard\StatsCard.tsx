import { ArrowDownRight, ArrowUpRight, LucideIcon } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

interface StatsCardProps {
  title: string;
  value: number | string;
  icon: LucideIcon;
  change?: {
    value: number;
    timeframe: string;
    trend: "up" | "down";
  };
  prefix?: string;
}

export const StatsCard = ({ title, value, icon: Icon, change, prefix = "" }: StatsCardProps) => {
  const getTrendColor = (trend: "up" | "down") => {
    return trend === "up" ? "text-emerald-500" : "text-red-500";
  };

  const TrendIcon = change?.trend === "up" ? ArrowUpRight : ArrowDownRight;

  return (
    <Card className="bg-[#1A1F2C] border-0 hover:bg-[#1A1F2C]/80 transition-colors">
      <CardContent className="p-6">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">{title}</span>
            <Icon className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-2xl font-bold">
                {prefix}{value}
              </p>
              {change && (
                <div className="flex items-center gap-1">
                  <TrendIcon className={`h-4 w-4 ${getTrendColor(change.trend)}`} />
                  <span className={`text-xs ${getTrendColor(change.trend)}`}>
                    {change.value}% {change.timeframe}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
export type LLMProvider = "openai" | "openrouter" | "groq";

export interface LLMModel {
  id: string;
  name: string;
  provider: LLMProvider;
  isRecommended?: boolean;
  isFree?: boolean;
  description?: string;
  contextWindow?: number;
}

export interface LLMConfig {
  provider: LLMProvider;
  apiKey: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  enabled: boolean;
}

export interface LLMResponse {
  success: boolean;
  content: string;
  error?: string;
}

export interface PrayerRequestAnalysis {
  isPrayerRequest: boolean;
  confidence: number; // 0-1
  category?: string;
  extractedRequest?: string;
  tags?: string[];
}

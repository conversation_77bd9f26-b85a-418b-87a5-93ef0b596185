import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { doc, updateDoc } from "firebase/firestore";
import { Ban, CheckCircle } from "lucide-react";

interface MemberStatusToggleProps {
  memberId: string;
  isActive: boolean;
  onStatusChange: () => void;
}

export const MemberStatusToggle = ({ memberId, isActive, onStatusChange }: MemberStatusToggleProps) => {
  const { toast } = useToast();

  const handleStatusToggle = async () => {
    try {
      await updateDoc(doc(db, "membros", memberId), {
        ativo: !isActive,
        updatedAt: new Date().getTime(),
      });

      toast({
        title: `Membro ${isActive ? "suspenso" : "reativado"} com sucesso!`,
      });

      onStatusChange();
    } catch (error) {
      console.error("Erro ao alterar status do membro:", error);
      toast({
        variant: "destructive",
        title: "Erro ao alterar status",
        description: "Tente novamente.",
      });
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={isActive ? "text-destructive" : "text-primary"}
        >
          {isActive ? <Ban className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isActive ? "Suspender" : "Reativar"} Membro
          </AlertDialogTitle>
          <AlertDialogDescription>
            Você tem certeza que deseja {isActive ? "suspender" : "reativar"} este membro?
            {isActive && " Membros suspensos não poderão participar de atividades."}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancelar</AlertDialogCancel>
          <AlertDialogAction onClick={handleStatusToggle}>
            Confirmar
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
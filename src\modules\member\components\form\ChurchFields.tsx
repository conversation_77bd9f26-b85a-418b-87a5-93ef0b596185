import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface ChurchFieldsProps {
  congregacoes: any[];
  cargos: any[];
  selectedCongregacao: string;
  selectedCargos: string[];
  setSelectedCongregacao: (value: string) => void;
  handleCargoToggle: (cargoId: string) => void;
}

export const ChurchFields = ({
  congregacoes,
  cargos,
  selectedCongregacao,
  selectedCargos,
  setSelectedCongregacao,
  handleCargoToggle,
}: ChurchFieldsProps) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <label htmlFor="congregacao" className="text-sm font-medium">
          Congregação
        </label>
        <Select value={selectedCongregacao} onValueChange={setSelectedCongregacao}>
          <SelectTrigger>
            <SelectValue placeholder="Selecione uma congregação" />
          </SelectTrigger>
          <SelectContent>
            <ScrollArea className="h-[200px]">
              {congregacoes?.map((congregacao) => (
                <SelectItem key={congregacao.id} value={congregacao.id}>
                  {congregacao.nome}
                </SelectItem>
              ))}
            </ScrollArea>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">
          Cargos
        </label>
        <div className="flex flex-wrap gap-2 p-2 border rounded-md max-h-[200px] overflow-y-auto">
          {cargos?.map((cargo) => (
            <TooltipProvider key={cargo.id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    variant={selectedCargos.includes(cargo.id) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleCargoToggle(cargo.id)}
                  >
                    {cargo.nome}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Clique para {selectedCargos.includes(cargo.id) ? 'remover' : 'adicionar'} o cargo</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
      </div>
    </div>
  );
};
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { db } from "@/lib/firebase";
import { doc, getDoc } from "firebase/firestore";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Printer } from "lucide-react";

interface Membro {
  nome: string;
  dataNascimento: string;
  rg: string;
  cpf: string;
  congregacao: string;
  dataBatismo?: string;
}

const CartaoMembro = () => {
  const { id } = useParams();
  const [membro, setMembro] = useState<Membro | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMembro = async () => {
      if (!id) return;
      
      try {
        const docRef = doc(db, "membros", id);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          setMembro(docSnap.data() as Membro);
        }
      } catch (error) {
        console.error("Erro ao buscar membro:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchMembro();
  }, [id]);

  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[60vh]">
        <div className="text-2xl animate-pulse">Carregando...</div>
      </div>
    );
  }

  if (!membro) {
    return (
      <div className="flex items-center justify-center h-[60vh]">
        <div className="text-2xl text-destructive">Membro não encontrado</div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto animate-fade-in">
      <div className="print:hidden mb-8 flex justify-between items-center">
        <h1 className="text-3xl font-bold">Cartão de Membro</h1>
        <Button onClick={handlePrint} className="transition-all hover:scale-105">
          <Printer className="mr-2 h-4 w-4" />
          Imprimir
        </Button>
      </div>

      <div className="bg-card rounded-lg shadow-lg p-8 print:shadow-none print:p-0">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold mb-2">Igreja Evangélica Assembleia de Deus</h2>
          <p className="text-muted-foreground">Ministério Deus é Ordem</p>
        </div>

        <div className="border-t border-b py-6 space-y-4">
          <div>
            <h3 className="text-lg font-semibold">{membro.nome}</h3>
            <p className="text-muted-foreground text-sm">
              Data de Nascimento: {format(new Date(membro.dataNascimento), "PPP", { locale: ptBR })}
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">RG</p>
              <p className="font-medium">{membro.rg}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">CPF</p>
              <p className="font-medium">{membro.cpf}</p>
            </div>
          </div>

          <div>
            <p className="text-sm text-muted-foreground">Congregação</p>
            <p className="font-medium">{membro.congregacao}</p>
          </div>

          {membro.dataBatismo && (
            <div>
              <p className="text-sm text-muted-foreground">Data de Batismo</p>
              <p className="font-medium">
                {format(new Date(membro.dataBatismo), "PPP", { locale: ptBR })}
              </p>
            </div>
          )}
        </div>

        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>Este cartão é de uso pessoal e intransferível</p>
          <p>Em caso de perda, favor comunicar à secretaria</p>
        </div>
      </div>
    </div>
  );
};

export default CartaoMembro;
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";

export const InstagramFeed = () => {
  const { data: posts, isLoading } = useQuery({
    queryKey: ["instagram-feed"],
    queryFn: async () => {
      // Simulando dados do Instagram por enquanto
      return [
        {
          id: "1",
          imageUrl: "https://via.placeholder.com/300",
          caption: "Culto de Domingo",
          likes: 120,
        },
        {
          id: "2",
          imageUrl: "https://via.placeholder.com/300",
          caption: "Encontro de Jovens",
          likes: 85,
        },
        {
          id: "3",
          imageUrl: "https://via.placeholder.com/300",
          caption: "Escola Bíblica",
          likes: 95,
        },
      ];
    },
  });

  if (isLoading) {
    return <div>Carregando feed do Instagram...</div>;
  }

  return (
    <Card className="bg-[#1A1F2C] border-0">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Instagram</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {posts?.map((post) => (
            <div
              key={post.id}
              className="relative group overflow-hidden rounded-lg aspect-square"
            >
              <img
                src={post.imageUrl}
                alt={post.caption}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                <div className="text-white text-center p-4">
                  <p className="text-sm">{post.caption}</p>
                  <p className="text-xs mt-2">❤️ {post.likes}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface Membro {
  id: string;
  nome: string;
  dataNascimento: string;
  dataBatismo?: string;
  rg: string;
  cpf: string;
  endereco: string;
  telefone: string;
  email: string;
  congregacao: string;
}

const CartaoMembro = () => {
  const { id } = useParams();

  const { data: membro, isLoading } = useQuery<Membro>({
    queryKey: ["membro", id],
    queryFn: async () => {
      if (!id) return null;
      const docRef = doc(db, "membros", id);
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) return null;
      return { id: docSnap.id, ...docSnap.data() } as Membro;
    },
  });

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto space-y-4">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-48" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-full" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!membro) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="bg-destructive/10">
          <CardContent className="p-6">
            <p className="text-destructive text-center font-medium">
              Membro não encontrado
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto animate-fade-in">
      <Card className="bg-card text-card-foreground">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Cartão de Membro</CardTitle>
          <p className="text-sm text-muted-foreground">
            Informações detalhadas do membro
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Nome</h3>
              <p className="text-base font-medium">{membro.nome}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Congregação</h3>
              <p className="text-base font-medium">{membro.congregacao}</p>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">
                Data de Nascimento
              </h3>
              <p className="text-base font-medium">
                {format(new Date(membro.dataNascimento), "PPP", { locale: ptBR })}
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">
                Data de Batismo
              </h3>
              <p className="text-base font-medium">
                {membro.dataBatismo
                  ? format(new Date(membro.dataBatismo), "PPP", { locale: ptBR })
                  : "Não batizado"}
              </p>
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">RG</h3>
              <p className="text-base font-medium">{membro.rg}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">CPF</h3>
              <p className="text-base font-medium">{membro.cpf}</p>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Endereço</h3>
            <p className="text-base font-medium">{membro.endereco}</p>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Telefone</h3>
              <p className="text-base font-medium">{membro.telefone}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
              <p className="text-base font-medium">{membro.email}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CartaoMembro;
import { Users, UserPlus, Shield } from "lucide-react";
import { ModuleConfig } from "@/types/module";
import ListagemUsuarios from "./pages/ListagemUsuarios";
import CadastroUsuarios from "./pages/CadastroUsuarios";
import GerenciamentoRoles from "./pages/GerenciamentoRoles";

export const usersModule: ModuleConfig = {
  id: "users",
  name: "Usuários",
  description: "Gerenciamento de usuários e permissões do sistema",
  icon: Users,
  routes: [
    {
      path: "/usuarios",
      component: ListagemUsuarios,
      name: "<PERSON>u<PERSON><PERSON><PERSON>",
      description: "Lista de usuários do sistema",
      icon: Users,
    },
    {
      path: "/usuarios/novo",
      component: CadastroUsuarios,
      name: "Novo Usuário",
      description: "Cadastrar novo usuário",
      icon: UserPlus,
    },
    {
      path: "/usuarios/roles",
      component: GerenciamentoRoles,
      name: "Roles e Permissões",
      description: "Gerenciar roles e permissões",
      icon: Shield,
    },
  ],
};

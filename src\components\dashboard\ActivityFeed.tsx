import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface Activity {
  title: string;
  description: string;
  timeAgo: string;
}

interface ActivityFeedProps {
  activities: Activity[];
}

export const ActivityFeed = ({ activities }: ActivityFeedProps) => {
  return (
    <Card className="bg-[#1A1F2C] border-0 hover:bg-[#1A1F2C]/80 transition-colors">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Distribuição por Categoria</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {activities.map((activity, index) => (
            <div key={index} className="flex items-start justify-between space-x-4">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">
                  {activity.title}
                </p>
                <p className="text-sm text-muted-foreground">
                  {activity.description}
                </p>
              </div>
              <div className="text-sm text-blue-400">
                {activity.timeAgo}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
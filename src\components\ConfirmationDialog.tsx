import { But<PERSON> } from "./ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "./ui/dialog";

interface ConfirmDialogProps {
  title: string;
  description: string;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const ConfirmationDialog = (props: ConfirmDialogProps) => {
  return (
    <Dialog open={props.isOpen} onOpenChange={props.onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-3xl">{props.title}</DialogTitle>
          <DialogDescription className="text-2xl font-semibold">{props.description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={props.onClose}>Cancel</Button>
          <Button variant="destructive" onClick={props.onConfirm}>Confirm</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
};
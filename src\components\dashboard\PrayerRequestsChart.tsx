import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON> } from "recharts";

interface PrayerRequestsChartProps {
  data: Array<{
    origem: string;
    quantidade: number;
  }>;
}

export const PrayerRequestsChart = ({ data }: PrayerRequestsChartProps) => {
  return (
    <Card className="bg-[#1A1F2C] border-0 hover:bg-[#1A1F2C]/80 transition-colors">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Balan<PERSON><PERSON></CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <XAxis
                dataKey="origem"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `${value}`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#1A1F2C",
                  border: "1px solid rgba(255,255,255,0.1)",
                  borderRadius: "8px",
                }}
                labelStyle={{ color: "white" }}
              />
              <Bar
                dataKey="quantidade"
                fill="hsl(var(--primary))"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
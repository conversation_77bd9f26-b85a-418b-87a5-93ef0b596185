import { Template, ProcessedTemplate, TemplateType } from '@/types/template';
import { doc, getDoc, collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db } from './firebase';
import { YouTubeComment } from '@/types/youtube';

export const getDefaultTemplate = async (type: TemplateType): Promise<Template | null> => {
  try {
    const settingsDoc = await getDoc(doc(db, 'system', 'settings'));
    if (!settingsDoc.exists()) return null;

    const settings = settingsDoc.data();
    const templateId = type === 'prayer' 
      ? settings.defaultPrayerTemplate 
      : settings.defaultAnnouncementTemplate;

    if (!templateId) return null;

    const templateDoc = await getDoc(doc(db, 'prayer-templates', templateId));
    if (!templateDoc.exists()) return null;

    return {
      id: templateDoc.id,
      ...templateDoc.data()
    } as Template;
  } catch (error) {
    console.error('Erro ao buscar template padrão:', error);
    return null;
  }
};

export const processTemplate = (
  template: string,
  variables: Record<string, string>
): string => {
  return template.replace(
    /{(\w+)}/g,
    (match, key) => variables[key] || match
  );
};

export const getLastUserRequest = async (
  authorId: string,
  type: TemplateType
): Promise<{ id: string; createdAt: string } | null> => {
  try {
    const q = query(
      collection(db, 'pedidos-oracao'),
      where('authorId', '==', authorId),
      where('tipo', '==', type),
      orderBy('createdAt', 'desc'),
      limit(1)
    );

    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) return null;

    const doc = querySnapshot.docs[0];
    return {
      id: doc.id,
      createdAt: doc.data().createdAt
    };
  } catch (error) {
    console.error('Erro ao buscar último pedido:', error);
    return null;
  }
};

export const processCommentWithTemplate = async (
  comment: YouTubeComment,
  type: TemplateType,
  extractedContent: string
): Promise<ProcessedTemplate> => {
  const template = await getDefaultTemplate(type);
  if (!template) {
    throw new Error(`Template padrão não encontrado para o tipo: ${type}`);
  }

  const content = processTemplate(template.template, {
    nome: comment.authorDisplayName,
    pedido: extractedContent,
    data: new Date().toLocaleDateString('pt-BR')
  });

  return {
    content,
    type,
    tags: ['Youtube', 'IA'],
    status: 'revisar',
    origem: 'youtube'
  };
};
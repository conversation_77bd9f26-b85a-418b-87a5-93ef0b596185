import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { useQuery } from "@tanstack/react-query";
import { addDoc, collection, getDocs } from "firebase/firestore";
import { useState } from "react";
import { useForm } from "react-hook-form";

interface FormData {
  nome: string;
  dataNascimento: string;
  rg: string;
  cpf: string;
  endereco: string;
  telefone: string;
  email: string;
  dataBatismo?: string;
  congregacao: string;
  cargoId?: string;
}

interface CadastroMembrosProps {
  onSuccess?: () => void;
}

interface Cargo {
  id: string;
  nome: string;
}

const CadastroMembros = ({ onSuccess }: CadastroMembrosProps) => {
  const { register, handleSubmit, reset } = useForm<FormData>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedCargo, setSelectedCargo] = useState<string>("");

  const { data: cargos } = useQuery({
    queryKey: ["cargos"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "cargos"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Cargo[];
    },
  });

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      await addDoc(collection(db, "membros"), {
        ...data,
        ativo: true,
        cargoId: selectedCargo,
        createdAt: new Date().getTime(),
      });

      toast({
        title: "Membro cadastrado com sucesso!",
        description: "O cadastro foi realizado com sucesso.",
      });

      reset();
      setSelectedCargo("");
      onSuccess?.();
    } catch (error) {
      console.error("Erro ao cadastrar membro:", error);
      toast({
        variant: "destructive",
        title: "Erro ao cadastrar membro",
        description: "Tente novamente.",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <label htmlFor="nome" className="text-sm font-medium">
          Nome Completo
        </label>
        <Input
          id="nome"
          {...register("nome", { required: true })}
          placeholder="Digite o nome completo"
          className="transition-all focus:scale-[1.01]"
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="dataNascimento" className="text-sm font-medium">
            Data de Nascimento
          </label>
          <Input
            id="dataNascimento"
            type="date"
            {...register("dataNascimento", { required: true })}
            className="transition-all focus:scale-[1.01]"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="dataBatismo" className="text-sm font-medium">
            Data de Batismo
          </label>
          <Input
            id="dataBatismo"
            type="date"
            {...register("dataBatismo")}
            className="transition-all focus:scale-[1.01]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="rg" className="text-sm font-medium">
            RG
          </label>
          <Input
            id="rg"
            {...register("rg", { required: true })}
            placeholder="Digite o RG"
            className="transition-all focus:scale-[1.01]"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="cpf" className="text-sm font-medium">
            CPF
          </label>
          <Input
            id="cpf"
            {...register("cpf", { required: true })}
            placeholder="Digite o CPF"
            className="transition-all focus:scale-[1.01]"
          />
        </div>
      </div>

      <div className="space-y-2">
        <label htmlFor="endereco" className="text-sm font-medium">
          Endereço
        </label>
        <Input
          id="endereco"
          {...register("endereco", { required: true })}
          placeholder="Digite o endereço completo"
          className="transition-all focus:scale-[1.01]"
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="telefone" className="text-sm font-medium">
            Telefone
          </label>
          <Input
            id="telefone"
            {...register("telefone", { required: true })}
            placeholder="Digite o telefone"
            className="transition-all focus:scale-[1.01]"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            E-mail
          </label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="Digite o e-mail"
            className="transition-all focus:scale-[1.01]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="congregacao" className="text-sm font-medium">
            Congregação
          </label>
          <Input
            id="congregacao"
            {...register("congregacao", { required: true })}
            placeholder="Digite a congregação"
            className="transition-all focus:scale-[1.01]"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="cargo" className="text-sm font-medium">
            Cargo
          </label>
          <Select value={selectedCargo} onValueChange={setSelectedCargo}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione um cargo" />
            </SelectTrigger>
            <SelectContent>
              {cargos?.map((cargo) => (
                <SelectItem key={cargo.id} value={cargo.id}>
                  {cargo.nome}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Button
        type="submit"
        className="w-full transition-all hover:scale-[1.02]"
        disabled={loading}
      >
        {loading ? (
          <span className="flex items-center gap-2">
            <span className="animate-pulse">Cadastrando...</span>
          </span>
        ) : (
          "Cadastrar Membro"
        )}
      </Button>
    </form>
  );
};

export default CadastroMembros;
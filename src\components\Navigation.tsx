import { cn } from "@/lib/utils";
import { modules } from "@/modules";
import { ChevronRight, Home, LogOut, User } from "lucide-react";
import { useTheme } from "next-themes";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { ThemeSelect } from "./ThemeSelect";
import { PrayerRequestNotification } from "./notifications/PrayerRequestNotification";
import { useQuery } from "@tanstack/react-query";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useEffect } from "react";
import {
  startAllMonitoring,
  stopAllMonitoring,
  isMonitoringActive,
} from "@/lib/monitoring";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/hooks/usePermissions";
import { PermissionGate } from "./PermissionGate";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
} from "./ui/sidebar";
import { useToast } from "./ui/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";

export const Navigation = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const { user, logout } = useAuth();
  const { canAccessRoute } = usePermissions();
  const isMobile = useIsMobile();

  // Buscar configurações do YouTube
  const { data: settings } = useQuery({
    queryKey: ["youtube-monitoring-settings"],
    queryFn: async () => {
      const docRef = doc(db, "system", "settings");
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        return docSnap.data();
      }
      return {};
    },
    refetchInterval: 60000, // Verificar a cada minuto
  });

  // Iniciar ou parar o monitoramento com base nas configurações
  useEffect(() => {
    if (
      settings?.youtubeMonitoring?.monitoringEnabled &&
      !isMonitoringActive()
    ) {
      startAllMonitoring();
      toast({
        title: "Monitoramento iniciado",
        description:
          "O sistema está monitorando transmissões ao vivo do YouTube.",
      });
    } else if (
      !settings?.youtubeMonitoring?.monitoringEnabled &&
      isMonitoringActive()
    ) {
      stopAllMonitoring();
    }

    return () => {
      // Parar o monitoramento quando o componente for desmontado
      if (isMonitoringActive()) {
        stopAllMonitoring();
      }
    };
  }, [settings?.youtubeMonitoring?.monitoringEnabled, toast]);

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Erro ao fazer logout:", error);
      toast({
        variant: "destructive",
        title: "Erro ao fazer logout",
        description: "Tente novamente.",
      });
    }
  };

  return (
    <SidebarProvider defaultOpen={!isMobile}>
      <Sidebar>
        <SidebarContent>
          <div className="flex flex-col justify-between items-center p-4 border-b border-sidebar-border">
            <div className="space-y-1 mb-4">
              <h1 className="text-lg font-bold text-sidebar-foreground">
                Assembleia de Deus
              </h1>
              <p className="text-xs text-sidebar-foreground/60">
                Mirassol D'Oeste
              </p>
            </div>
            <div className="flex items-center gap-2">
              <PrayerRequestNotification
                enabled={settings?.youtubeMonitoring?.notificationsEnabled}
              />
              <ThemeSelect />
            </div>
          </div>

          <div className="px-2 py-4">
            <SidebarGroup>
              <SidebarGroupLabel>Menu Principal</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      isActive={location.pathname === "/"}
                      tooltip="Página inicial do sistema"
                    >
                      <Link
                        to="/"
                        className={cn(
                          "flex items-center gap-2 w-full px-3 py-2 rounded-lg transition-colors",
                          location.pathname === "/"
                            ? "bg-sidebar-accent text-sidebar-accent-foreground"
                            : "hover:bg-sidebar-accent/50"
                        )}
                      >
                        <Home className="h-4 w-4" />
                        <span>Início</span>
                        <ChevronRight className="h-4 w-4 ml-auto" />
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {modules.map((module) => {
              // Filtrar rotas baseado nas permissões do usuário
              const accessibleRoutes = module.routes.filter(route =>
                !route.hidden && canAccessRoute(route.path)
              );

              // Só mostrar o módulo se houver rotas acessíveis
              if (accessibleRoutes.length === 0) return null;

              return (
                <SidebarGroup key={module.id} className="mt-4">
                  <SidebarGroupLabel>{module.name}</SidebarGroupLabel>
                  <SidebarGroupContent>
                    <SidebarMenu>
                      {accessibleRoutes.map((route) => (
                        <SidebarMenuItem key={route.path}>
                          <SidebarMenuButton
                            asChild
                            isActive={location.pathname === route.path}
                            tooltip={route.description}
                          >
                            <Link
                              to={route.path}
                              className={cn(
                                "flex items-center gap-2 w-full px-3 py-2 rounded-lg transition-colors",
                                location.pathname === route.path
                                  ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                  : "hover:bg-sidebar-accent/50"
                              )}
                            >
                              {route.icon && <route.icon className="h-4 w-4" />}
                              <span>{route.name}</span>
                              <ChevronRight className="h-4 w-4 ml-auto" />
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                </SidebarGroup>
              );
            })}

            <SidebarGroup className="mt-4">
              <SidebarGroupLabel>Conta</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {user && (
                    <SidebarMenuItem>
                      <div className="px-3 py-2 text-sm">
                        <div className="flex items-center gap-2 mb-1">
                          <User className="h-4 w-4" />
                          <span className="font-medium">{user.displayName}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {user.roles.map((role) => (
                            <span key={role} className="mr-1 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-lg">
                              {role}
                            </span>
                          ))}
                        </div>
                      </div>
                    </SidebarMenuItem>
                  )}
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      onClick={handleLogout}
                      tooltip="Sair do sistema"
                      className="w-full px-3 py-2 rounded-lg text-destructive hover:bg-destructive/10 transition-colors"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Sair</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </div>
        </SidebarContent>
      </Sidebar>
    </SidebarProvider>
  );
};

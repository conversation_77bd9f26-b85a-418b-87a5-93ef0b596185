import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { useQuery } from "@tanstack/react-query";
import { addDoc, collection, deleteDoc, doc, getDocs } from "firebase/firestore";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";

interface Cargo {
  id: string;
  nome: string;
  descricao: string;
}

const GerenciamentoCargos = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [nome, setNome] = useState("");
  const [descricao, setDescricao] = useState("");
  const { toast } = useToast();

  const { data: cargos, refetch } = useQuery({
    queryKey: ["cargos"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "cargos"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Cargo[];
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addDoc(collection(db, "cargos"), {
        nome,
        descricao,
        createdAt: new Date().getTime(),
      });

      toast({
        title: "Cargo cadastrado com sucesso!",
        description: "O cargo foi adicionado à lista.",
      });

      setNome("");
      setDescricao("");
      setIsDialogOpen(false);
      refetch();
    } catch (error) {
      console.error("Erro ao cadastrar cargo:", error);
      toast({
        variant: "destructive",
        title: "Erro ao cadastrar cargo",
        description: "Tente novamente.",
      });
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteDoc(doc(db, "cargos", id));
      toast({
        title: "Cargo removido com sucesso!",
      });
      refetch();
    } catch (error) {
      console.error("Erro ao remover cargo:", error);
      toast({
        variant: "destructive",
        title: "Erro ao remover cargo",
        description: "Tente novamente.",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Cargos</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Novo Cargo
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Cadastro de Cargo</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="nome" className="text-sm font-medium">
                  Nome do Cargo
                </label>
                <Input
                  id="nome"
                  value={nome}
                  onChange={(e) => setNome(e.target.value)}
                  placeholder="Digite o nome do cargo"
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="descricao" className="text-sm font-medium">
                  Descrição
                </label>
                <Input
                  id="descricao"
                  value={descricao}
                  onChange={(e) => setDescricao(e.target.value)}
                  placeholder="Digite a descrição do cargo"
                  required
                />
              </div>
              <Button type="submit" className="w-full">
                Cadastrar Cargo
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Descrição</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {cargos?.map((cargo) => (
              <TableRow key={cargo.id}>
                <TableCell className="font-medium">{cargo.nome}</TableCell>
                <TableCell>{cargo.descricao}</TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(cargo.id)}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default GerenciamentoCargos;
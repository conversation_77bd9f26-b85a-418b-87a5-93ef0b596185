/**
 * Script de inicialização do sistema de usuários
 * Execute este script após a primeira instalação para configurar o sistema
 */

import { initializeSystemRoles } from '../lib/users';
import { db } from '../lib/firebase';
import { doc, setDoc, getDoc, query, collection, where, getDocs } from 'firebase/firestore';
import { SystemRoles } from '../types/user';

interface InitConfig {
  adminEmails: string[];
  systemName: string;
  defaultUserRole: string;
}

const DEFAULT_CONFIG: InitConfig = {
  adminEmails: [
    '<EMAIL>',
    '<EMAIL>',
    // Adicione outros emails de admin aqui
  ],
  systemName: 'Belém Connect - IEADMDO',
  defaultUserRole: SystemRoles.USER
};

/**
 * Inicializa as configurações do sistema
 */
async function initSystemSettings(config: InitConfig): Promise<void> {
  try {
    const settingsRef = doc(db, 'system', 'settings');
    const settingsDoc = await getDoc(settingsRef);

    const currentSettings = settingsDoc.exists() ? settingsDoc.data() : {};

    const userSystemSettings = {
      ...currentSettings,
      userSystem: {
        initialized: true,
        initializedAt: Date.now(),
        adminEmails: config.adminEmails,
        defaultUserRole: config.defaultUserRole,
        systemName: config.systemName,
        version: '1.0.0'
      }
    };

    await setDoc(settingsRef, userSystemSettings, { merge: true });
    console.log('✅ Configurações do sistema inicializadas');
  } catch (error) {
    console.error('❌ Erro ao inicializar configurações:', error);
    throw error;
  }
}

/**
 * Cria usuário admin inicial se não existir
 */
async function createInitialAdmin(adminEmail: string): Promise<void> {
  try {
    // Verificar se já existe um usuário admin
    const usersQuery = query(
      collection(db, 'users'),
      where('roles', 'array-contains', SystemRoles.ADMIN)
    );
    const querySnapshot = await getDocs(usersQuery);
    if (!querySnapshot.empty) {
      console.log('ℹ️  Admin já existe, pulando criação');
      return;
    }

    // TODO: Remover este bloco após criar usuário inicial

    // Este é um placeholder - em produção, você precisará:
    // 1. Criar o usuário no Firebase Auth manualmente
    // 2. Executar este script para criar o perfil no Firestore

    console.log(`ℹ️  Para criar o admin inicial:`);
    console.log(`1. Crie o usuário ${adminEmail} no Firebase Auth Console`);
    console.log(`2. Faça o primeiro login para criar o perfil automaticamente`);
    console.log(`3. O sistema detectará o email como admin e atribuirá as permissões`);

  } catch (error) {
    console.error('❌ Erro ao configurar admin inicial:', error);
    throw error;
  }
}

/**
 * Verifica se o sistema já foi inicializado
 */
async function checkIfInitialized(): Promise<boolean> {
  try {
    const settingsRef = doc(db, 'system', 'settings');
    const settingsDoc = await getDoc(settingsRef);

    if (settingsDoc.exists()) {
      const data = settingsDoc.data();
      return data.userSystem?.initialized === true;
    }

    return false;
  } catch (error) {
    console.error('❌ Erro ao verificar inicialização:', error);
    return false;
  }
}

/**
 * Valida a configuração do Firebase
 */
async function validateFirebaseConfig(): Promise<void> {
  try {
    // Testar conexão com Firestore
    const testRef = doc(db, 'system', 'test');
    await setDoc(testRef, { test: true, timestamp: Date.now() });

    console.log('✅ Conexão com Firebase validada');
  } catch (error) {
    console.error('❌ Erro na conexão com Firebase:', error);
    throw error;
  }
}

/**
 * Função principal de inicialização
 */
export async function initializeUserSystem(config: InitConfig = DEFAULT_CONFIG): Promise<void> {
  console.log('🚀 Iniciando configuração do sistema de usuários...\n');

  try {
    // 1. Validar configuração do Firebase
    console.log('1. Validando configuração do Firebase...');
    await validateFirebaseConfig();

    // 2. Verificar se já foi inicializado
    console.log('2. Verificando se sistema já foi inicializado...');
    const isInitialized = await checkIfInitialized();

    if (isInitialized) {
      console.log('ℹ️  Sistema já foi inicializado anteriormente');
      console.log('   Para reinicializar, remova o documento system/settings do Firestore');
      return;
    }

    // 3. Inicializar roles do sistema
    console.log('3. Inicializando roles do sistema...');
    await initializeSystemRoles();

    // 4. Configurar configurações do sistema
    console.log('4. Configurando sistema...');
    await initSystemSettings(config);

    // 5. Instruções para admin inicial
    console.log('5. Configurando admin inicial...');
    if (config.adminEmails.length > 0) {
      await createInitialAdmin(config.adminEmails[0]);
    }

    console.log('\n✅ Sistema de usuários inicializado com sucesso!');
    console.log('\n📋 Próximos passos:');
    console.log('1. Crie o usuário admin no Firebase Auth Console');
    console.log('2. Faça o primeiro login para ativar o perfil');
    console.log('3. Comece a criar outros usuários através da interface');
    console.log('\n🔧 Configuração atual:');
    console.log(`   - Emails de admin: ${config.adminEmails.join(', ')}`);
    console.log(`   - Role padrão: ${config.defaultUserRole}`);
    console.log(`   - Nome do sistema: ${config.systemName}`);

  } catch (error) {
    console.error('\n❌ Erro durante a inicialização:', error);
    throw error;
  }
}

/**
 * Função para resetar o sistema (usar com cuidado!)
 */
export async function resetUserSystem(): Promise<void> {
  console.log('⚠️  ATENÇÃO: Esta operação irá resetar todo o sistema de usuários!');
  console.log('   Todos os usuários e configurações serão perdidos.');

  // Em produção, adicionar confirmação adicional
  const confirmed = confirm('Tem certeza que deseja resetar o sistema? Esta ação não pode ser desfeita.');

  if (!confirmed) {
    console.log('❌ Operação cancelada');
    return;
  }

  try {
    // Remover configurações
    const settingsRef = doc(db, 'system', 'settings');
    const settingsDoc = await getDoc(settingsRef);

    if (settingsDoc.exists()) {
      const data = settingsDoc.data();
      delete data.userSystem;
      await setDoc(settingsRef, data);
    }

    console.log('✅ Sistema resetado. Execute initializeUserSystem() para reconfigurar.');

  } catch (error) {
    console.error('❌ Erro ao resetar sistema:', error);
    throw error;
  }
}

// // Executar se chamado diretamente
// if (typeof window === 'undefined') {
//   // Executando no Node.js
//   initializeUserSystem()
//     .then(() => {
//       console.log('Inicialização concluída');
//       process.exit(0);
//     })
//     .catch((error) => {
//       console.error('Erro na inicialização:', error);
//       process.exit(1);
//     });
// }

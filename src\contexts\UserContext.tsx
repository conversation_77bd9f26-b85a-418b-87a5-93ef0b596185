import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  User,
  Role,
  UserContextType,
  CreateUserRequest,
  UpdateUserRequest
} from '@/types/user';
import {
  createUser,
  getAllUsers,
  getAllRoles,
  updateUser as updateUserData,
  deactivateUser,
  deleteRole,
  createRole
} from '@/lib/users';
import { useAuth } from './AuthContext';
import { useToast } from '@/components/ui/use-toast';

const UserContext = createContext<UserContextType | undefined>(undefined);

export const useUsers = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUsers must be used within a UserProvider');
  }
  return context;
};

interface UserProviderProps {
  children: React.ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const { user: currentUser, permissions } = useAuth();
  const { toast } = useToast();

  // Carregar usuários
  const loadUsers = async () => {
    if (!permissions.canViewUsers) return;

    try {
      setLoading(true);
      const usersData = await getAllUsers();
      setUsers(usersData);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar usuários',
        description: 'Não foi possível carregar a lista de usuários.'
      });
    } finally {
      setLoading(false);
    }
  };

  // Carregar roles
  const loadRoles = async () => {
    try {
      const rolesData = await getAllRoles();
      setRoles(rolesData);
    } catch (error) {
      console.error('Erro ao carregar roles:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar roles',
        description: 'Não foi possível carregar as roles do sistema.'
      });
    }
  };

  const handleCreateRole = async (roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> => {
    if (!permissions.canManageRoles) {
      throw new Error('Sem permissão para gerenciar roles');
    }

    try {
      setLoading(true);
      const newRole = await createRole(roleData);
      setRoles(prev => [newRole, ...prev]);
      return newRole;
    } catch (error) {
      console.error('Erro ao criar role:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao criar role',
        description: 'Não foi possível criar a role.'
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRole = async (roleId: string): Promise<void> => {
    if (!permissions.canManageRoles) {
      throw new Error('Sem permissão para gerenciar roles');
    }

    try {
      setLoading(true);
      await deleteRole(roleId);

      setRoles(prev => prev.filter(role => role.id !== roleId));

      toast({
        title: 'Role excluída com sucesso!',
        description: 'A role foi excluída do sistema.'
      });
    } catch (error) {
      console.error('Erro ao excluir role:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao excluir role',
        description: 'Não foi possível excluir a role.'
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    if (currentUser && permissions.canViewUsers) {
      loadUsers();
    }
    loadRoles();
  }, [currentUser, permissions.canViewUsers]);

  // Criar usuário
  const handleCreateUser = async (userData: CreateUserRequest): Promise<void> => {
    if (!permissions.canCreateUsers || !currentUser) {
      throw new Error('Sem permissão para criar usuários');
    }

    try {
      setLoading(true);
      const newUser = await createUser(userData, currentUser.id);
      setUsers(prev => [newUser, ...prev]);

      toast({
        title: 'Usuário criado com sucesso!',
        description: `${newUser.displayName} foi adicionado ao sistema.`
      });
    } catch (error: any) {
      console.error('Erro ao criar usuário:', error);

      let errorMessage = 'Erro desconhecido ao criar usuário.';
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'Este email já está sendo usado por outro usuário.';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'A senha deve ter pelo menos 6 caracteres.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Email inválido.';
      }

      toast({
        variant: 'destructive',
        title: 'Erro ao criar usuário',
        description: errorMessage
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Atualizar usuário
  const handleUpdateUser = async (userId: string, userData: UpdateUserRequest): Promise<void> => {
    if (!permissions.canEditUsers || !currentUser) {
      throw new Error('Sem permissão para editar usuários');
    }

    try {
      setLoading(true);
      await updateUserData(userId, userData, currentUser.id);

      setUsers(prev => prev.map(user =>
        user.id === userId
          ? { ...user, ...userData, updatedAt: Date.now() }
          : user
      ));

      toast({
        title: 'Usuário atualizado com sucesso!',
        description: 'As alterações foram salvas.'
      });
    } catch (error: any) {
      console.error('Erro ao atualizar usuário:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao atualizar usuário',
        description: error.message || 'Não foi possível salvar as alterações.'
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Desativar usuário (soft delete)
  const handleDeleteUser = async (userId: string): Promise<void> => {
    if (!permissions.canDeleteUsers) {
      throw new Error('Sem permissão para desativar usuários');
    }

    if (userId === currentUser?.id) {
      throw new Error('Não é possível desativar seu próprio usuário');
    }

    try {
      setLoading(true);
      await deactivateUser(userId);

      setUsers(prev => prev.map(user =>
        user.id === userId
          ? { ...user, isActive: false, updatedAt: Date.now() }
          : user
      ));

      toast({
        title: 'Usuário desativado com sucesso!',
        description: 'O usuário foi desativado e não poderá mais acessar o sistema.'
      });
    } catch (error) {
      console.error('Erro ao desativar usuário:', error);
      toast({
        variant: 'destructive',
        title: 'Erro ao desativar usuário',
        description: 'Não foi possível desativar o usuário.'
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Buscar usuário por ID
  const getUserById = (userId: string): User | undefined => {
    return users.find(user => user.id === userId);
  };

  // Refresh functions
  const refreshUsers = async (): Promise<void> => {
    await loadUsers();
  };

  const refreshRoles = async (): Promise<void> => {
    await loadRoles();
  };

  const value: UserContextType = {
    users,
    roles,
    deleteRole: handleDeleteRole,
    createRole: handleCreateRole,
    loading,
    createUser: handleCreateUser,
    updateUser: handleUpdateUser,
    deleteUser: handleDeleteUser,
    getUserById,
    refreshUsers,
    refreshRoles
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

import {
  startYouTubeMonitoring,
  stopYouTubeMonitoring,
  getYouTubeSettings,
} from "./youtube";
import { collection, getDocs, query, where } from "firebase/firestore";
import { db } from "./firebase";
import { Congregation, CongregationService } from "@/types/congregation";
import { YouTubeMonitoringSchedule } from "@/types/youtube";

let youtubeMonitoringInterval: NodeJS.Timeout | null = null;
let scheduleCheckInterval: NodeJS.Timeout | null = null;

// Buscar todas as congregações e seus horários de culto
const getCongregationServices = async (): Promise<CongregationService[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, "congregacoes"));
    const congregations = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Congregation[];

    // Extrair todos os serviços de todas as congregações
    const allServices: CongregationService[] = [];
    congregations.forEach((congregation) => {
      if (congregation.services && congregation.services.length > 0) {
        allServices.push(...congregation.services);
      }
    });

    return allServices;
  } catch (error) {
    console.error("Erro ao buscar congregações:", error);
    return [];
  }
};

// Verificar se há algum culto programado para começar em breve ou terminar em breve
const checkScheduledServices = async (): Promise<boolean> => {
  try {
    const settings = await getYouTubeSettings();

    // Se o agendamento não estiver ativado, retornar false
    if (!settings.youtubeMonitoring?.scheduleEnabled) {
      return false;
    }

    const services = await getCongregationServices();
    const now = new Date();
    const currentDay = now.getDay(); // 0-6, onde 0 é domingo

    // Verificar se há algum culto programado para hoje
    const todayServices = services.filter(
      (service) => service.dayOfWeek === currentDay
    );

    if (todayServices.length === 0) {
      return false;
    }

    // Verificar se estamos próximos do horário de algum culto
    for (const service of todayServices) {
      const [hours, minutes] = service.time.split(":").map(Number);

      // Criar data para o horário do culto hoje
      const serviceTime = new Date();
      serviceTime.setHours(hours, minutes, 0, 0);

      // Tempo em minutos antes do culto para iniciar o monitoramento (padrão: 30 minutos)
      const preStartMinutes = 30;

      // Tempo em minutos após o culto para continuar monitorando (padrão: 60 minutos)
      const postEndMinutes = 60;

      // Calcular o tempo de início e fim do monitoramento
      const monitoringStartTime = new Date(
        serviceTime.getTime() - preStartMinutes * 60000
      );
      const monitoringEndTime = new Date(
        serviceTime.getTime() + postEndMinutes * 60000
      );

      // Verificar se estamos dentro do período de monitoramento
      if (now >= monitoringStartTime && now <= monitoringEndTime) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("Erro ao verificar horários de culto:", error);
    return false;
  }
};

// Iniciar todos os serviços de monitoramento
export const startAllMonitoring = async (): Promise<void> => {
  try {
    console.log("Iniciando todos os serviços de monitoramento...");

    // Verificar se o monitoramento já está ativo
    if (isMonitoringActive()) {
      console.log("Monitoramento já está ativo, reiniciando...");
      stopAllMonitoring();
    }

    // Iniciar monitoramento do YouTube com tratamento de erros
    try {
      console.log("Iniciando monitoramento do YouTube...");
      youtubeMonitoringInterval = await startYouTubeMonitoring();
      console.log("Monitoramento do YouTube iniciado com sucesso");
    } catch (youtubeError) {
      console.error("Erro ao iniciar monitoramento do YouTube:", youtubeError);
      // Continuar mesmo com erro para iniciar o agendamento
    }

    // Iniciar verificação de agendamento
    if (!scheduleCheckInterval) {
      console.log("Iniciando verificação de agendamento...");
      scheduleCheckInterval = setInterval(async () => {
        try {
          const settings = await getYouTubeSettings();

          // Se o agendamento estiver ativado
          if (settings.youtubeMonitoring?.scheduleEnabled) {
            const shouldMonitor = await checkScheduledServices();

            // Se devemos monitorar com base no agendamento, mas o monitoramento não está ativo
            if (shouldMonitor && !isMonitoringActive()) {
              console.log("Iniciando monitoramento baseado no agendamento");
              youtubeMonitoringInterval = await startYouTubeMonitoring();
            }
            // Se não devemos monitorar com base no agendamento, mas o monitoramento está ativo
            else if (!shouldMonitor && isMonitoringActive()) {
              console.log("Parando monitoramento baseado no agendamento");
              stopYouTubeMonitoring(youtubeMonitoringInterval);
              youtubeMonitoringInterval = null;
            }
          }
        } catch (scheduleError) {
          console.error("Erro na verificação de agendamento:", scheduleError);
          // Continuar executando mesmo em caso de erro
        }
      }, 60000); // Verificar a cada minuto
      console.log("Verificação de agendamento iniciada com sucesso");
    }

    console.log("Todos os serviços de monitoramento iniciados com sucesso");
  } catch (error) {
    console.error("Erro ao iniciar serviços de monitoramento:", error);
    // Garantir que não há intervalos ativos em caso de erro
    stopAllMonitoring();
    throw error; // Propagar o erro para tratamento adequado
  }
};

// Parar todos os serviços de monitoramento
export const stopAllMonitoring = (): void => {
  try {
    console.log("Parando todos os serviços de monitoramento...");

    // Parar monitoramento do YouTube
    try {
      console.log("Parando monitoramento do YouTube...");
      if (youtubeMonitoringInterval) {
        stopYouTubeMonitoring(youtubeMonitoringInterval);
        console.log("Monitoramento do YouTube parado com sucesso");
      } else {
        console.log("Nenhum monitoramento do YouTube ativo para parar");
      }
    } catch (youtubeError) {
      console.error("Erro ao parar monitoramento do YouTube:", youtubeError);
      // Continuar mesmo com erro
    }

    // Resetar a variável de intervalo
    youtubeMonitoringInterval = null;

    // Parar verificação de agendamento
    try {
      console.log("Parando verificação de agendamento...");
      if (scheduleCheckInterval) {
        clearInterval(scheduleCheckInterval);
        scheduleCheckInterval = null;
        console.log("Verificação de agendamento parada com sucesso");
      } else {
        console.log("Nenhuma verificação de agendamento ativa para parar");
      }
    } catch (scheduleError) {
      console.error("Erro ao parar verificação de agendamento:", scheduleError);
      // Continuar mesmo com erro
      scheduleCheckInterval = null; // Garantir que a variável seja resetada
    }

    console.log("Todos os serviços de monitoramento parados com sucesso");
  } catch (error) {
    console.error("Erro ao parar serviços de monitoramento:", error);
    // Garantir que as variáveis sejam resetadas mesmo em caso de erro
    youtubeMonitoringInterval = null;
    scheduleCheckInterval = null;
  }
};

// Verificar se o monitoramento está ativo
export const isMonitoringActive = (): boolean => {
  return youtubeMonitoringInterval !== null;
};

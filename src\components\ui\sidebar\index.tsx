import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { PanelLeft } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { useSidebar } from "./context"
import { sidebarMenuButtonVariants } from "./variants"
import type { SidebarMenuButtonProps } from "./types"

// Export components from their respective files
export { useSidebar } from "./context"
export { SidebarProvider } from "./components/SidebarProvider"
export { Sidebar } from "./components/Sidebar"
export { 
  SidebarMenu, 
  SidebarMenuItem, 
  SidebarMenuButton 
} from "./components/SidebarMenu"

// Export other components
export const SidebarTrigger = React.forwardRef<
  React.ElementRef<typeof Button>,
  React.ComponentProps<typeof Button>
>(({ className, onClick, ...props }, ref) => {
  const { toggleSidebar } = useSidebar()

  return (
    <Button
      ref={ref}
      data-sidebar="trigger"
      variant="ghost"
      size="icon"
      className={cn("h-7 w-7", className)}
      onClick={(event) => {
        onClick?.(event)
        toggleSidebar()
      }}
      {...props}
    >
      <PanelLeft />
      <span className="sr-only">Toggle Sidebar</span>
    </Button>
  )
})
SidebarTrigger.displayName = "SidebarTrigger"
import { useEffect, useState } from "react";
import {
  collection,
  onSnapshot,
  query,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { PedidoOracao } from "@/types/prayer";
import { useToast } from "@/components/ui/use-toast";
import { Bell } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface PrayerRequestNotificationProps {
  enabled?: boolean;
}

export const PrayerRequestNotification = ({
  enabled = true,
}: PrayerRequestNotificationProps) => {
  const [newRequests, setNewRequests] = useState<PedidoOracao[]>([]);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (!enabled) return;

    // Criar query para pedidos pendentes
    const q = query(
      collection(db, "pedidos-oracao"),
      where("status", "in", ["pendente", "revisao"]),
      where("origem", "==", "youtube"),
      orderBy("createdAt", "desc"),
      limit(10)
    );

    // Escutar por novos pedidos
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const newDocs = snapshot
        .docChanges()
        .filter((change) => change.type === "added")
        .map(
          (change) =>
            ({
              id: change.doc.id,
              ...change.doc.data(),
            } as PedidoOracao)
        );

      if (newDocs.length > 0) {
        setNewRequests((prev) => [...newDocs, ...prev]);

        // Mostrar notificação para o primeiro pedido novo
        const firstRequest = newDocs[0];
        toast({
          title: "Novo pedido de oração do YouTube",
          description: `De: ${firstRequest.nome}`,
          action: (
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/pulpito")}
            >
              Ver
            </Button>
          ),
        });
      }
    });

    return () => unsubscribe();
  }, [enabled, toast, navigate]);

  if (newRequests.length === 0) return null;

  return (
    <Button
      variant="outline"
      size="sm"
      className="relative"
      onClick={() => navigate("/pulpito")}
    >
      <Bell className="h-4 w-4 mr-2" />
      Pedidos
      <Badge className="ml-2 bg-red-500">{newRequests.length}</Badge>
    </Button>
  );
};

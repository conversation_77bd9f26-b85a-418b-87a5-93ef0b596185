export interface User {
  id: string; // Firebase Auth UID
  email: string;
  displayName: string;
  roles: string[];
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  createdBy: string; // UID do admin que criou
  lastLoginAt?: number;
  profilePicture?: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  isSystemRole: boolean; // Roles do sistema não podem ser deletadas
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string; // ex: 'users', 'prayers', 'events'
  action: string; // ex: 'create', 'read', 'update', 'delete'
}

export interface UserProfile extends User {
  // Dados adicionais do perfil que podem ser expandidos
  phone?: string;
  congregation?: string;
  position?: string;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  displayName: string;
  roles: string[];
}

export interface UpdateUserRequest {
  displayName?: string;
  roles?: string[];
  isActive?: boolean;
}

export interface UserPermissions {
  canCreateUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canManageRoles: boolean;
  canViewUsers: boolean;
  canManagePrayers: boolean;
  canManageEvents: boolean;
  canManageSettings: boolean;
}

// Enum para roles padrão do sistema
export enum SystemRoles {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  USER = 'user',
  VIEWER = 'viewer'
}

// Enum para permissões básicas
export enum SystemPermissions {
  // Usuários
  USERS_CREATE = 'users:create',
  USERS_READ = 'users:read',
  USERS_UPDATE = 'users:update',
  USERS_DELETE = 'users:delete',

  // Roles
  ROLES_MANAGE = 'roles:manage',

  // Pedidos de Oração
  PRAYERS_CREATE = 'prayers:create',
  PRAYERS_READ = 'prayers:read',
  PRAYERS_UPDATE = 'prayers:update',
  PRAYERS_DELETE = 'prayers:delete',
  PRAYERS_MODERATE = 'prayers:moderate',

  // Eventos
  EVENTS_CREATE = 'events:create',
  EVENTS_READ = 'events:read',
  EVENTS_UPDATE = 'events:update',
  EVENTS_DELETE = 'events:delete',

  // Membros
  MEMBERS_CREATE = 'members:create',
  MEMBERS_READ = 'members:read',
  MEMBERS_UPDATE = 'members:update',
  MEMBERS_DELETE = 'members:delete',

  // Congregações
  CONGREGATIONS_CREATE = 'congregations:create',
  CONGREGATIONS_READ = 'congregations:read',
  CONGREGATIONS_UPDATE = 'congregations:update',
  CONGREGATIONS_DELETE = 'congregations:delete',

  // Cargos
  POSITIONS_CREATE = 'positions:create',
  POSITIONS_READ = 'positions:read',
  POSITIONS_UPDATE = 'positions:update',
  POSITIONS_DELETE = 'positions:delete',

  // Configurações
  SETTINGS_MANAGE = 'settings:manage',

  // Sistema
  SYSTEM_ADMIN = 'system:admin'
}


export const getPermissionDescription = (permissionId: string) => {
  const descriptions: Record<string, string> = {
    [SystemPermissions.USERS_CREATE]: "Criar novos usuários",
    [SystemPermissions.USERS_READ]: "Visualizar usuários",
    [SystemPermissions.USERS_UPDATE]: "Editar usuários",
    [SystemPermissions.USERS_DELETE]: "Excluir usuários",
    [SystemPermissions.ROLES_MANAGE]: "Gerenciar roles",
    [SystemPermissions.PRAYERS_CREATE]: "Criar pedidos de oração",
    [SystemPermissions.PRAYERS_READ]: "Visualizar pedidos de oração",
    [SystemPermissions.PRAYERS_UPDATE]: "Editar pedidos de oração",
    [SystemPermissions.PRAYERS_DELETE]: "Excluir pedidos de oração",
    [SystemPermissions.PRAYERS_MODERATE]: "Moderar pedidos de oração",
    [SystemPermissions.EVENTS_CREATE]: "Criar eventos",
    [SystemPermissions.EVENTS_READ]: "Visualizar eventos",
    [SystemPermissions.EVENTS_UPDATE]: "Editar eventos",
    [SystemPermissions.EVENTS_DELETE]: "Excluir eventos",
    [SystemPermissions.MEMBERS_CREATE]: "Criar membros",
    [SystemPermissions.MEMBERS_READ]: "Visualizar membros",
    [SystemPermissions.MEMBERS_UPDATE]: "Editar membros",
    [SystemPermissions.MEMBERS_DELETE]: "Excluir membros",
    [SystemPermissions.CONGREGATIONS_CREATE]: "Criar congregações",
    [SystemPermissions.CONGREGATIONS_READ]: "Visualizar congregações",
    [SystemPermissions.CONGREGATIONS_UPDATE]: "Editar congregações",
    [SystemPermissions.CONGREGATIONS_DELETE]: "Excluir congregações",
    [SystemPermissions.POSITIONS_CREATE]: "Criar cargos",
    [SystemPermissions.POSITIONS_READ]: "Visualizar cargos",
    [SystemPermissions.POSITIONS_UPDATE]: "Editar cargos",
    [SystemPermissions.POSITIONS_DELETE]: "Excluir cargos",
    [SystemPermissions.SETTINGS_MANAGE]: "Gerenciar configurações",
    [SystemPermissions.SYSTEM_ADMIN]: "Administração do sistema"
  };

  return descriptions[permissionId] || permissionId;
};

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  permissions: UserPermissions;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  refreshUser: () => Promise<void>;
  logout: () => Promise<void>;
}

export interface UserContextType {
  users: User[];
  roles: Role[];
  deleteRole: (roleId: string) => Promise<void>;
  createRole: (roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Role>;
  loading: boolean;
  createUser: (userData: CreateUserRequest) => Promise<void>;
  updateUser: (userId: string, userData: UpdateUserRequest) => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
  getUserById: (userId: string) => User | undefined;
  refreshUsers: () => Promise<void>;
  refreshRoles: () => Promise<void>;
}

import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { useQuery } from "@tanstack/react-query";
import { addDoc, collection, doc, getDoc, getDocs, updateDoc } from "firebase/firestore";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { ChurchFields } from "./form/ChurchFields";
import { ContactFields } from "./form/ContactFields";
import { DocumentFields } from "./form/DocumentFields";
import { PersonalInfoFields } from "./form/PersonalInfoFields";

interface FormData {
  nome: string;
  dataNascimento: string;
  rg: string;
  cpf: string;
  endereco: string;
  telefone: string;
  email: string;
  dataBatismo?: string;
  congregacaoId: string;
  cargoIds: string[];
}

interface MemberFormProps {
  memberId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const MemberForm = ({ memberId, onSuccess, onCancel }: MemberFormProps) => {
  const { register, handleSubmit, reset, setValue } = useForm<FormData>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [selectedCargos, setSelectedCargos] = useState<string[]>([]);
  const [selectedCongregacao, setSelectedCongregacao] = useState<string>("");

  const { data: cargos } = useQuery({
    queryKey: ["cargos"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "cargos"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
    },
  });

  const { data: congregacoes } = useQuery({
    queryKey: ["congregacoes"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "congregacoes"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));
    },
  });

  useEffect(() => {
    const fetchMemberData = async () => {
      if (!memberId) return;

      try {
        const docRef = doc(db, "membros", memberId);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const data = docSnap.data();
          Object.entries(data).forEach(([key, value]) => {
            setValue(key as keyof FormData, value);
          });
          setSelectedCongregacao(data.congregacaoId || "");
          setSelectedCargos(data.cargoIds || []);
        }
      } catch (error) {
        console.error("Erro ao carregar dados do membro:", error);
        toast({
          variant: "destructive",
          title: "Erro ao carregar dados",
          description: "Não foi possível carregar os dados do membro.",
        });
      }
    };

    fetchMemberData();
  }, [memberId, setValue, toast]);

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      const memberData = {
        ...data,
        congregacaoId: selectedCongregacao,
        cargoIds: selectedCargos,
        updatedAt: new Date().getTime(),
      };

      if (memberId) {
        await updateDoc(doc(db, "membros", memberId), memberData);
        toast({ title: "Membro atualizado com sucesso!" });
      } else {
        await addDoc(collection(db, "membros"), {
          ...memberData,
          createdAt: new Date().getTime(),
        });
        toast({ title: "Membro cadastrado com sucesso!" });
      }

      reset();
      setSelectedCargos([]);
      setSelectedCongregacao("");
      onSuccess?.();
    } catch (error) {
      console.error("Erro ao salvar membro:", error);
      toast({
        variant: "destructive",
        title: `Erro ao ${memberId ? 'atualizar' : 'cadastrar'} membro`,
        description: "Tente novamente.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCargoToggle = (cargoId: string) => {
    setSelectedCargos((prev) => {
      if (prev.includes(cargoId)) {
        return prev.filter((id) => id !== cargoId);
      }
      return [...prev, cargoId];
    });
  };

  return (
    <ScrollArea className="h-[80vh] pr-4">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <PersonalInfoFields register={register} />
        <DocumentFields register={register} />
        <ContactFields register={register} />
        <ChurchFields
          congregacoes={congregacoes || []}
          cargos={cargos || []}
          selectedCongregacao={selectedCongregacao}
          selectedCargos={selectedCargos}
          setSelectedCongregacao={setSelectedCongregacao}
          handleCargoToggle={handleCargoToggle}
        />

        <div className="flex justify-end gap-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          )}
          <Button
            type="submit"
            className="transition-all hover:scale-[1.02]"
            disabled={loading}
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <span className="animate-pulse">
                  {memberId ? 'Atualizando...' : 'Cadastrando...'}
                </span>
              </span>
            ) : (
              memberId ? "Salvar Alterações" : "Cadastrar Membro"
            )}
          </Button>
        </div>
      </form>
    </ScrollArea>
  );
};

export default MemberForm;
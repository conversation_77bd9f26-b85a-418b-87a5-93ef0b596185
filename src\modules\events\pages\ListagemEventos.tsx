import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { db } from "@/lib/firebase";
import { useToast } from "@/components/ui/use-toast";
import { useQuery } from "@tanstack/react-query";
import { collection, getDocs, orderBy, query } from "firebase/firestore";
import { CalendarPlus, ChevronRight } from "lucide-react";
import { Link } from "react-router-dom";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Event } from "../types/event";

const ListagemEventos = () => {
  const { toast } = useToast();

  const { data: events, isLoading } = useQuery({
    queryKey: ["events"],
    queryFn: async () => {
      try {
        const eventsRef = collection(db, "events");
        const q = query(eventsRef, orderBy("date", "asc"));
        const querySnapshot = await getDocs(q);
        return querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Event[];
      } catch (error) {
        console.error("Erro ao buscar eventos:", error);
        toast({
          variant: "destructive",
          title: "Erro ao carregar eventos",
          description: "Tente novamente mais tarde.",
        });
        return [];
      }
    },
  });

  if (isLoading) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Eventos</h1>
          <p className="text-muted-foreground">
            Gerencie os eventos da igreja
          </p>
        </div>
        <Button asChild>
          <Link to="/eventos/novo" className="gap-2">
            <CalendarPlus className="h-4 w-4" />
            Novo Evento
          </Link>
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {events?.map((event) => (
          <Card key={event.id} className="hover:bg-muted/50 transition-colors">
            <CardHeader>
              <CardTitle>{event.title}</CardTitle>
              <CardDescription>
                {format(new Date(event.date), "PPP", { locale: ptBR })} às{" "}
                {event.time}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                {event.description}
              </p>
              <Button variant="outline" asChild className="w-full">
                <Link to={`/eventos/${event.id}`} className="gap-2">
                  Ver detalhes
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ListagemEventos;
import { useState } from "react";
import { Input } from "@/components/ui/input"; // Importando o componente Input do ShadCN

export default function TagInput() {
  const [tags, setTags] = useState([]);
  const [currentTag, setCurrentTag] = useState("");
  const [filteredSuggestions, setFilteredSuggestions] = useState([]);
  
  // Lista de sugestões de tags predefinidas (pode ser dinâmica se necessário)
  const allTags = ["React", "JavaScript", "Node.js", "CSS", "HTML", "Vue", "Tailwind", "Next.js", "TypeScript"];
  
  // Filtra as sugestões com base no que o usuário digitou
  const handleInputChange = (e) => {
    const value = e.target.value;
    setCurrentTag(value);
    
    if (value) {
      // Filtra tags que começam com o texto digitado
      setFilteredSuggestions(allTags.filter(tag => tag.toLowerCase().startsWith(value.toLowerCase())));
    } else {
      setFilteredSuggestions([]);
    }
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter" && currentTag.trim() !== "") {
      // Adiciona a tag se não estiver já na lista
      if (!tags.includes(currentTag.trim())) {
        setTags([...tags, currentTag.trim()]);
      }
      setCurrentTag(""); // Limpa o input após adicionar a tag
      event.preventDefault();
    }
  };

  const handleTagClick = (tag) => {
    if (!tags.includes(tag)) {
      setTags([...tags, tag]);
      setCurrentTag(""); // Limpa o campo após adicionar
    }
  };

  const removeTag = (tagToRemove) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  return (
    <div className="flex flex-col items-start space-y-2">
      <div className="flex flex-wrap gap-2">
        {tags.map((tag, index) => (
          <div
            key={index}
            className="flex items-center bg-blue-500 text-white rounded-full px-4 py-1"
          >
            {tag}
            <button
              className="ml-2 text-xl cursor-pointer"
              onClick={() => removeTag(tag)}
            >
              &times;
            </button>
          </div>
        ))}
      </div>

      <Input
        type="text"
        value={currentTag}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder="Digite e pressione Enter"
        className="mt-2"
      />
      
      {filteredSuggestions.length > 0 && (
        <div className="mt-2 w-full max-h-60 overflow-y-auto bg-white border rounded-lg shadow-lg">
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={index}
              className="p-2 cursor-pointer hover:bg-gray-200"
              onClick={() => handleTagClick(suggestion)}
            >
              {suggestion}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

import { db } from "./firebase";
import {
  doc,
  getDoc,
  setDoc,
  collection,
  addDoc,
  query,
  where,
  getDocs,
  updateDoc,
} from "firebase/firestore";
import {
  YouTubeComment,
  YouTubeLiveStream,
  YouTubeMonitoringSettings,
} from "@/types/youtube";
import { PedidoOracao } from "@/types/prayer";
import { analyzePrayerRequest, getLLMConfig } from "./llm";

// Função para buscar as configurações do YouTube
export const getYouTubeSettings = async (): Promise<{
  youtubeChannelId?: string;
  youtubeApiKey?: string;
  youtubeMonitoring?: YouTubeMonitoringSettings;
}> => {
  const docRef = doc(db, "system", "settings");
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return docSnap.data() as any;
  }

  return {};
};

// Função para verificar se há transmissões ao vivo
export const checkForLiveStreams =
  async (): Promise<YouTubeLiveStream | null> => {
    try {
      const settings = await getYouTubeSettings();

      if (
        !settings.youtubeChannelId ||
        !settings.youtubeApiKey ||
        !settings.youtubeMonitoring?.monitoringEnabled
      ) {
        console.log("Monitoramento do YouTube não configurado ou desativado");
        return null;
      }

      const apiKey = settings.youtubeApiKey;
      const channelId = settings.youtubeChannelId;

      // Buscar transmissões ao vivo do canal
      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/search?part=snippet&channelId=${channelId}&eventType=live&type=video&key=${apiKey}`
      );

      if (!response.ok) {
        console.error("Erro na API do YouTube:", response.status);
        throw new Error(`Erro na API do YouTube: ${response.status}`);
      }

      const data = await response.json();

      // Atualizar timestamp da última verificação
      await updateMonitoringStatus({
        lastCheckedAt: new Date().toISOString(),
      });

      // Se não houver transmissões ao vivo
      if (!data.items || data.items.length === 0) {
        // Se havia uma transmissão ao vivo anteriormente, atualizar status
        if (settings.youtubeMonitoring?.currentLiveStreamId) {
          // Marcar a transmissão como encerrada no Firestore
          await updateLiveStreamStatus(
            settings.youtubeMonitoring.currentLiveStreamId,
            false
          );

          // Atualizar status de monitoramento
          await updateMonitoringStatus({
            currentLiveStreamId: undefined,
          });

          console.log(
            `Transmissão ${settings.youtubeMonitoring.currentLiveStreamId} encerrada`
          );
        }
        return null;
      }

      // Encontrou uma transmissão ao vivo
      const liveStream = data.items[0];
      const videoId = liveStream.id.videoId;

      // Se for uma nova transmissão ao vivo
      if (videoId !== settings.youtubeMonitoring?.currentLiveStreamId) {
        const streamInfo: YouTubeLiveStream = {
          id: liveStream.id.videoId,
          title: liveStream.snippet.title,
          channelId: liveStream.snippet.channelId,
          channelTitle: liveStream.snippet.channelTitle,
          startTime: liveStream.snippet.publishedAt,
          isLive: true,
          videoId: liveStream.id.videoId,
          thumbnailUrl: liveStream.snippet.thumbnails.high.url,
        };

        // Salvar informações da transmissão
        await saveLiveStreamInfo(streamInfo);

        // Atualizar status de monitoramento
        await updateMonitoringStatus({
          currentLiveStreamId: videoId,
        });

        return streamInfo;
      }

      return null;
    } catch (error) {
      console.error("Erro ao verificar transmissões ao vivo:", error);
      return null;
    }
  };

// Função para salvar informações da transmissão ao vivo
const saveLiveStreamInfo = async (
  streamInfo: YouTubeLiveStream
): Promise<void> => {
  try {
    const docRef = doc(db, "youtube-streams", streamInfo.id);
    await setDoc(docRef, {
      ...streamInfo,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao salvar informações da transmissão:", error);
  }
};

// Função para atualizar o status de uma transmissão ao vivo
export const updateLiveStreamStatus = async (
  streamId: string,
  isLive: boolean
): Promise<void> => {
  try {
    const docRef = doc(db, "youtube-streams", streamId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const streamData = docSnap.data() as YouTubeLiveStream;

      // Se o status já for o mesmo, não faz nada
      if (streamData.isLive === isLive) {
        return;
      }

      // Atualizar apenas o campo isLive e adicionar endTime se estiver encerrando
      const updates: any = {
        isLive,
        updatedAt: new Date().toISOString(),
      };

      // Se estiver encerrando a transmissão, adicionar o timestamp de término
      if (!isLive) {
        updates.endTime = new Date().toISOString();
      }

      await updateDoc(docRef, updates);

      console.log(
        `Status da transmissão ${streamId} atualizado para ${
          isLive ? "ao vivo" : "encerrada"
        }`
      );
    } else {
      console.warn(`Transmissão ${streamId} não encontrada no Firestore`);
    }
  } catch (error) {
    console.error("Erro ao atualizar status da transmissão:", error);
  }
};

// Função para atualizar status de monitoramento
const updateMonitoringStatus = async (
  updates: Partial<YouTubeMonitoringSettings>
): Promise<void> => {
  try {
    const settings = await getYouTubeSettings();

    const updatedMonitoring = {
      ...settings.youtubeMonitoring,
      ...updates,
    };

    const docRef = doc(db, "system", "settings");
    await updateDoc(docRef, {
      youtubeMonitoring: updatedMonitoring,
    });
  } catch (error) {
    console.error("Erro ao atualizar status de monitoramento:", error);
  }
};

// Função para buscar comentários de uma transmissão ao vivo
export const fetchLiveStreamComments = async (
  videoId: string
): Promise<YouTubeComment[]> => {
  try {
    console.log(`Buscando comentários para o vídeo: ${videoId}`);
    const settings = await getYouTubeSettings();

    if (!settings.youtubeApiKey) {
      console.warn("API Key do YouTube não configurada");
      return [];
    }

    if (!settings.youtubeMonitoring?.commentProcessingEnabled) {
      console.warn("Processamento de comentários desativado nas configurações");
      return [];
    }

    const apiKey = settings.youtubeApiKey;

    // Primeiro, vamos verificar se é uma transmissão ao vivo ou um vídeo normal
    // Para transmissões ao vivo, usamos a API de chat ao vivo
    // Para vídeos normais, usamos a API de comentários

    // Verificar se é uma transmissão ao vivo ativa
    const videoDetailsUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,liveStreamingDetails&id=${videoId}&key=${apiKey}`;
    console.log(`Verificando detalhes do vídeo: ${videoDetailsUrl}`);

    const videoDetailsResponse = await fetch(videoDetailsUrl);

    if (!videoDetailsResponse.ok) {
      const errorText = await videoDetailsResponse.text();
      console.error(
        `Erro ao obter detalhes do vídeo (${videoDetailsResponse.status}): ${errorText}`
      );
      throw new Error(
        `Erro ao obter detalhes do vídeo: ${videoDetailsResponse.status} - ${errorText}`
      );
    }

    const videoDetails = await videoDetailsResponse.json();

    if (!videoDetails.items || videoDetails.items.length === 0) {
      console.log(`Vídeo ${videoId} não encontrado`);
      return [];
    }

    const videoItem = videoDetails.items[0];
    const isLiveStream =
      videoItem.liveStreamingDetails &&
      videoItem.liveStreamingDetails.actualStartTime &&
      !videoItem.liveStreamingDetails.actualEndTime;

    console.log(
      `Vídeo ${videoId} é ${
        isLiveStream ? "uma transmissão ao vivo" : "um vídeo normal"
      }`
    );

    let url: string;
    if (isLiveStream) {
      // Para transmissões ao vivo, usamos a API de chat ao vivo
      // Primeiro, precisamos obter o ID do chat ao vivo
      const liveChatId = videoItem.liveStreamingDetails.activeLiveChatId;

      if (!liveChatId) {
        console.log(`Transmissão ao vivo ${videoId} não tem chat ativo`);
        return [];
      }

      // Agora, buscamos as mensagens do chat ao vivo
      url = `https://www.googleapis.com/youtube/v3/liveChat/messages?part=snippet,authorDetails&liveChatId=${liveChatId}&maxResults=200&key=${apiKey}`;
      console.log(`Buscando mensagens do chat ao vivo: ${url}`);
    } else {
      // Para vídeos normais, usamos a API de comentários
      url = `https://www.googleapis.com/youtube/v3/commentThreads?part=snippet&videoId=${videoId}&maxResults=100&order=time&key=${apiKey}`;
      console.log(`Buscando comentários do vídeo: ${url}`);
    }

    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `Erro na API do YouTube (${response.status}): ${errorText}`
      );

      // Verificar se o erro é devido a comentários desativados
      if (
        errorText.includes("commentsDisabled") ||
        errorText.includes("disabled comments")
      ) {
        console.log("Comentários estão desativados para este vídeo");
        throw new Error("COMMENTS_DISABLED");
      }

      throw new Error(
        `Erro na API do YouTube: ${response.status} - ${errorText}`
      );
    }

    const data = await response.json();

    if (!data.items || data.items.length === 0) {
      console.log("Nenhum comentário ou mensagem de chat encontrado");
      return [];
    }

    console.log(
      `Resposta da API do YouTube: ${data.items.length} itens encontrados`
    );

    // Mapear comentários ou mensagens de chat
    let comments: YouTubeComment[] = [];

    if (isLiveStream) {
      // Processar mensagens de chat ao vivo
      comments = data.items
        .map((item: any) => {
          try {
            return {
              id: item.id,
              authorDisplayName: item.authorDetails.displayName,
              authorProfileImageUrl: item.authorDetails.profileImageUrl,
              textDisplay: item.snippet.displayMessage,
              publishedAt: item.snippet.publishedAt,
              likeCount: 0, // Chat ao vivo não tem contagem de likes
              videoId,
              processed: false,
            };
          } catch (err) {
            console.error("Erro ao processar mensagem de chat:", err, item);
            return null;
          }
        })
        .filter(Boolean) as YouTubeComment[];
    } else {
      // Processar comentários normais
      comments = data.items
        .map((item: any) => {
          try {
            return {
              id: item.id,
              authorDisplayName:
                item.snippet.topLevelComment.snippet.authorDisplayName,
              authorProfileImageUrl:
                item.snippet.topLevelComment.snippet.authorProfileImageUrl,
              textDisplay: item.snippet.topLevelComment.snippet.textDisplay,
              publishedAt: item.snippet.topLevelComment.snippet.publishedAt,
              likeCount: item.snippet.topLevelComment.snippet.likeCount,
              videoId,
              processed: false,
            };
          } catch (err) {
            console.error("Erro ao processar comentário:", err, item);
            return null;
          }
        })
        .filter(Boolean) as YouTubeComment[];
    }

    console.log(`${comments.length} comentários processados com sucesso`);
    return comments;
  } catch (error) {
    console.error("Erro ao buscar comentários:", error);
    return [];
  }
};

// Função para processar comentários e criar pedidos de oração
export const processComments = async (
  comments: YouTubeComment[]
): Promise<void> => {
  try {
    console.log(`Processando ${comments.length} comentários`);
    const settings = await getYouTubeSettings();

    if (!settings.youtubeMonitoring?.commentProcessingEnabled) {
      console.log(
        "Processamento de comentários desativado, apenas salvando comentários"
      );
      // Se o processamento de comentários estiver desativado, apenas salvar comentários
      for (const comment of comments) {
        await saveComment(comment);
      }
      return;
    }

    // Processar comentários
    let processedCount = 0;
    let prayerRequestCount = 0;

    for (const comment of comments) {
      console.log(
        `Processando comentário: "${comment.textDisplay.substring(
          0,
          50
        )}..." de ${comment.authorDisplayName}`
      );

      // Verificar se o comentário já foi processado
      const existingComments = await getCommentById(comment.id);
      if (existingComments.length > 0 && existingComments[0].processed) {
        console.log(`Comentário já foi processado anteriormente, pulando...`);
        continue;
      }

      let isPrayerRequest = false;
      let extractedRequest = comment.textDisplay;
      let tags = ["youtube", "automático"];

      // Usar LLM se estiver ativado
      // Primeiro, verificamos se o processamento com LLM está ativado nas configurações do YouTube
      // Depois, obtemos as configurações de LLM separadamente, pois elas estão em outra parte do documento
      if (settings.youtubeMonitoring?.llmEnabled) {
        // Obter configurações de LLM
        const llmConfig = await getLLMConfig();
        console.log(`Configurações de LLM:`, llmConfig);

        // Verificar se o LLM está habilitado
        if (llmConfig?.enabled) {
          console.log(`Analisando comentário com LLM (${llmConfig.provider})`);
          try {
            // Analisar o comentário com o LLM configurado
            const analysis = await analyzePrayerRequest(comment.textDisplay);
            console.log(`Resultado da análise LLM:`, analysis);

            isPrayerRequest = analysis.isPrayerRequest;

            // Usar o pedido extraído se disponível
            if (analysis.extractedRequest) {
              extractedRequest = analysis.extractedRequest;
            }

            // Adicionar tags se disponíveis
            if (analysis.tags && analysis.tags.length > 0) {
              tags = [...tags, ...analysis.tags];
            }

            console.log(
              `Comentário analisado com LLM: ${
                isPrayerRequest ? "Pedido de oração" : "Não é pedido"
              }`
            );
          } catch (llmError) {
            console.error("Erro ao analisar comentário com LLM:", llmError);
            // Fallback para o método simples em caso de erro
            isPrayerRequest = isPotentialPrayerRequest(comment.textDisplay);
            console.log(
              `Usando método simples como fallback: ${
                isPrayerRequest ? "Pedido de oração" : "Não é pedido"
              }`
            );
          }
        } else {
          // LLM está desativado nas configurações gerais
          console.log(
            `LLM desativado nas configurações gerais, usando método simples`
          );
          isPrayerRequest = isPotentialPrayerRequest(comment.textDisplay);
          console.log(
            `Resultado da análise simples: ${
              isPrayerRequest ? "Pedido de oração" : "Não é pedido"
            }`
          );
        }
      } else {
        // Método simples se o LLM estiver desativado nas configurações do YouTube
        console.log(
          `LLM desativado nas configurações do YouTube, usando método simples`
        );
        isPrayerRequest = isPotentialPrayerRequest(comment.textDisplay);
        console.log(
          `Resultado da análise simples: ${
            isPrayerRequest ? "Pedido de oração" : "Não é pedido"
          }`
        );
      }

      if (isPrayerRequest) {
        console.log(`Criando pedido de oração a partir do comentário`);
        // Criar pedido de oração
        await createPrayerRequestFromComment(comment, extractedRequest, tags);
        prayerRequestCount++;
      }

      // Marcar comentário como processado
      await saveComment({
        ...comment,
        processed: true,
      });

      processedCount++;
    }

    console.log(
      `Processamento concluído: ${processedCount} comentários processados, ${prayerRequestCount} pedidos de oração criados`
    );
  } catch (error) {
    console.error("Erro ao processar comentários:", error);
  }
};

// Função simples para verificar se um comentário pode ser um pedido de oração
// Esta função seria substituída por uma análise mais sofisticada com LLM
const isPotentialPrayerRequest = (text: string): boolean => {
  const prayerKeywords = [
    "oração",
    "ore",
    "orar",
    "orando",
    "reza",
    "rezar",
    "prece",
    "preces",
    "ajuda",
    "ajude",
    "socorro",
    "doente",
    "doença",
    "hospital",
    "internado",
    "cirurgia",
    "operação",
    "saúde",
    "problema",
    "dificuldade",
    "deus",
    "jesus",
    "senhor",
    "espírito",
    "fé",
    "graça",
    "milagre",
    "bênção",
    "benção",
  ];

  const lowerText = text.toLowerCase();
  return prayerKeywords.some((keyword) => lowerText.includes(keyword));
};

// Função para salvar um comentário no Firestore
const saveComment = async (comment: YouTubeComment): Promise<void> => {
  try {
    const docRef = doc(db, "youtube-comments", comment.id);
    await setDoc(docRef, {
      ...comment,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Erro ao salvar comentário:", error);
  }
};

// Função para buscar um comentário pelo ID
const getCommentById = async (commentId: string): Promise<YouTubeComment[]> => {
  try {
    const q = query(
      collection(db, "youtube-comments"),
      where("id", "==", commentId)
    );
    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => doc.data() as YouTubeComment);
  } catch (error) {
    console.error("Erro ao buscar comentário:", error);
    return [];
  }
};

// Função para buscar a transmissão ao vivo ativa
export const getActiveLiveStream =
  async (): Promise<YouTubeLiveStream | null> => {
    try {
      console.log("Buscando transmissão ao vivo ativa...");
      const settings = await getYouTubeSettings();

      // Se não houver uma transmissão ao vivo atual nas configurações
      if (!settings.youtubeMonitoring?.currentLiveStreamId) {
        console.log("Nenhuma transmissão ao vivo ativa nas configurações");
        return null;
      }

      // Buscar a transmissão ao vivo pelo ID
      const streamId = settings.youtubeMonitoring.currentLiveStreamId;
      const docRef = doc(db, "youtube-streams", streamId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        console.log(`Transmissão ${streamId} não encontrada no Firestore`);
        return null;
      }

      const streamData = docSnap.data() as YouTubeLiveStream;

      // Verificar se a transmissão está ativa
      if (!streamData.isLive) {
        console.log(`Transmissão ${streamId} não está mais ativa`);
        return null;
      }

      console.log(`Transmissão ao vivo ativa encontrada: ${streamData.title}`);
      return streamData;
    } catch (error) {
      console.error("Erro ao buscar transmissão ao vivo ativa:", error);
      return null;
    }
  };

// Função para criar um pedido de oração a partir de um comentário
const createPrayerRequestFromComment = async (
  comment: YouTubeComment,
  extractedRequest: string = comment.textDisplay,
  tags: string[] = ["youtube", "automático"]
): Promise<string | void> => {
  try {
    console.log(`Criando pedido de oração para: ${comment.authorDisplayName}`);
    console.log(`Texto do pedido: ${extractedRequest}`);
    console.log(`Tags: ${tags.join(", ")}`);

    const pedido: Omit<PedidoOracao, "id"> = {
      titulo: `Pedido do YouTube - ${comment.authorDisplayName}`,
      descricao: `Pedido extraído de comentário do YouTube`,
      nome: comment.authorDisplayName,
      pedido: extractedRequest,
      tipo: "pedido",
      origem: "youtube",
      tags: tags,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: "pendente", // Garantir que o status seja "pendente" para aparecer na lista
      lido: false, // Mantido para compatibilidade com código antigo
    };

    const docRef = await addDoc(collection(db, "pedidos-oracao"), pedido);
    console.log(`Pedido de oração criado com ID: ${docRef.id}`);

    // Enviar notificação se estiver ativado
    const settings = await getYouTubeSettings();
    if (settings.youtubeMonitoring?.notificationsEnabled) {
      // Aqui seria implementada a lógica de notificação
      console.log(
        "Notificação: Novo pedido de oração criado a partir de comentário do YouTube"
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao criar pedido de oração:", error);
    throw error; // Propagar o erro para que possa ser tratado pelo chamador
  }
};

// Função para iniciar o monitoramento de transmissões ao vivo
export const startYouTubeMonitoring =
  async (): Promise<NodeJS.Timeout | null> => {
    try {
      console.log("Iniciando monitoramento do YouTube...");
      const settings = await getYouTubeSettings();

      if (!settings.youtubeMonitoring?.monitoringEnabled) {
        console.log("Monitoramento do YouTube desativado nas configurações");
        return null;
      }

      const checkInterval =
        settings.youtubeMonitoring.checkIntervalSeconds * 1000 || 60000;
      console.log(
        `Intervalo de verificação configurado para ${
          checkInterval / 1000
        } segundos`
      );

      // Verificar imediatamente
      console.log("Verificando transmissões ao vivo imediatamente...");
      const liveStream = await checkForLiveStreams();

      if (liveStream) {
        console.log(
          `Transmissão ao vivo encontrada: ${liveStream.title} (ID: ${liveStream.videoId})`
        );

        if (settings.youtubeMonitoring.commentProcessingEnabled) {
          console.log(
            `Buscando comentários para a transmissão ${liveStream.videoId}...`
          );
          // Se encontrou uma transmissão ao vivo, buscar comentários
          const comments = await fetchLiveStreamComments(liveStream.videoId);

          if (comments.length > 0) {
            console.log(
              `${comments.length} comentários encontrados, processando...`
            );
            await processComments(comments);
          } else {
            console.log("Nenhum comentário encontrado para processar");
          }
        } else {
          console.log(
            "Processamento de comentários desativado nas configurações"
          );
        }
      } else {
        console.log("Nenhuma transmissão ao vivo encontrada");
      }

      // Configurar intervalo para verificações periódicas
      console.log(
        `Configurando verificações periódicas a cada ${
          checkInterval / 1000
        } segundos`
      );
      const intervalId = setInterval(async () => {
        try {
          console.log("Executando verificação periódica...");
          const currentSettings = await getYouTubeSettings();

          if (!currentSettings.youtubeMonitoring?.monitoringEnabled) {
            console.log(
              "Monitoramento desativado, parando verificações periódicas"
            );
            clearInterval(intervalId);
            return;
          }

          const stream = await checkForLiveStreams();

          if (stream) {
            console.log(
              `Transmissão ao vivo ativa: ${stream.title} (ID: ${stream.videoId})`
            );

            if (currentSettings.youtubeMonitoring.commentProcessingEnabled) {
              console.log(
                `Buscando comentários para a transmissão ${stream.videoId}...`
              );
              const comments = await fetchLiveStreamComments(stream.videoId);

              if (comments.length > 0) {
                console.log(
                  `${comments.length} comentários encontrados, processando...`
                );
                await processComments(comments);
              } else {
                console.log("Nenhum comentário novo encontrado para processar");
              }
            } else {
              console.log(
                "Processamento de comentários desativado nas configurações"
              );
            }
          } else {
            console.log(
              "Nenhuma transmissão ao vivo encontrada na verificação periódica"
            );
          }
        } catch (intervalError) {
          console.error("Erro durante a verificação periódica:", intervalError);
          // Continuar executando mesmo em caso de erro
        }
      }, checkInterval);

      console.log("Monitoramento do YouTube iniciado com sucesso");
      return intervalId;
    } catch (error) {
      console.error("Erro ao iniciar monitoramento do YouTube:", error);
      return null;
    }
  };

// Função para parar o monitoramento
export const stopYouTubeMonitoring = (
  intervalId: NodeJS.Timeout | null
): void => {
  if (intervalId) {
    clearInterval(intervalId);
  }
};

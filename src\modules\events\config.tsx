import { ModuleConfig } from "@/types/module";
import { Calendar, CalendarPlus } from "lucide-react";
import CadastroEventos from "./pages/CadastroEventos";
import ListagemEventos from "./pages/ListagemEventos";
import EventoDetalhes from "./pages/EventoDetalhes";

export const eventsModule: ModuleConfig = {
  id: "events",
  name: "Eventos",
  description: "Gerenciamento de eventos da igreja",
  icon: Calendar,
  routes: [
    {
      path: "/eventos",
      component: ListagemEventos,
      name: "Eventos",
      description: "Lista de eventos da igreja",
      icon: Calendar,
    },
    {
      path: "/eventos/novo",
      component: CadastroEventos,
      name: "Novo Evento",
      description: "Cadastrar novo evento",
      icon: CalendarPlus,
    },
    {
      path: "/eventos/:id",
      component: EventoDetalhes,
      name: "Detalhes do Evento",
      description: "Visualizar detalhes do evento",
      icon: Calendar,
      hidden: true,
    },
  ],
};
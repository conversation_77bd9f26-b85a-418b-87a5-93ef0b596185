import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useUsers } from "@/contexts/UserContext";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { User, CreateUserRequest, UpdateUserRequest } from "@/types/user";
import { Loader2, AlertTriangle } from "lucide-react";
import { sanitizeInput, validateUserData, isStrongPassword, canUserModifyUser } from "@/lib/security";
import { useToast } from "@/components/ui/use-toast";

interface UserFormProps {
  user?: User; // Se fornecido, é edição. Se não, é criação
  onSuccess: () => void;
  onCancel: () => void;
}

interface FormData {
  displayName: string;
  email: string;
  password?: string;
  confirmPassword?: string;
  roles: string[];
}

const UserForm = ({ user, onSuccess, onCancel }: UserFormProps) => {
  const { createUser, updateUser, roles, loading } = useUsers();
  const { user: currentUser } = useAuth();
  const { toast } = useToast();
  const [selectedRoles, setSelectedRoles] = useState<string[]>(user?.roles || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<{ isValid: boolean; errors: string[] }>({ isValid: true, errors: [] });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<FormData>({
    defaultValues: {
      displayName: user?.displayName || "",
      email: user?.email || "",
      roles: user?.roles || []
    }
  });

  const password = watch("password");
  const isEditing = !!user;

  useEffect(() => {
    setValue("roles", selectedRoles);
  }, [selectedRoles, setValue]);

  const handleRoleToggle = (roleId: string) => {
    setSelectedRoles(prev => {
      if (prev.includes(roleId)) {
        return prev.filter(id => id !== roleId);
      } else {
        return [...prev, roleId];
      }
    });
  };

  // Validar senha em tempo real
  const handlePasswordChange = (password: string) => {
    if (password) {
      setPasswordStrength(isStrongPassword(password));
    } else {
      setPasswordStrength({ isValid: true, errors: [] });
    }
  };

  const onSubmit = async (data: FormData) => {
    if (!currentUser) {
      toast({
        variant: 'destructive',
        title: 'Erro de autenticação',
        description: 'Usuário não autenticado.'
      });
      return;
    }

    // Sanitizar dados de entrada
    const sanitizedData = {
      displayName: sanitizeInput(data.displayName),
      email: data.email.toLowerCase().trim(),
      roles: selectedRoles
    };

    // Validar dados
    const validation = validateUserData(sanitizedData);
    if (!validation.isValid) {
      toast({
        variant: 'destructive',
        title: 'Dados inválidos',
        description: validation.errors.join(', ')
      });
      return;
    }

    // Verificar permissões de modificação
    if (isEditing && user) {
      const canModify = canUserModifyUser(currentUser, user, 'update');
      if (!canModify.canModify) {
        toast({
          variant: 'destructive',
          title: 'Sem permissão',
          description: canModify.reason || 'Você não tem permissão para modificar este usuário.'
        });
        return;
      }
    }

    // Validar senha para novos usuários
    if (!isEditing) {
      if (!data.password) {
        toast({
          variant: 'destructive',
          title: 'Senha obrigatória',
          description: 'Senha é obrigatória para novos usuários.'
        });
        return;
      }

      if (!passwordStrength.isValid) {
        toast({
          variant: 'destructive',
          title: 'Senha fraca',
          description: passwordStrength.errors.join(', ')
        });
        return;
      }

      if (data.password !== data.confirmPassword) {
        toast({
          variant: 'destructive',
          title: 'Senhas não coincidem',
          description: 'As senhas digitadas não são iguais.'
        });
        return;
      }
    }

    setIsSubmitting(true);

    try {
      if (isEditing) {
        // Atualizar usuário existente
        const updateData: UpdateUserRequest = {
          displayName: sanitizedData.displayName,
          roles: sanitizedData.roles
        };
        await updateUser(user.id, updateData);
      } else {
        // Criar novo usuário
        const createData: CreateUserRequest = {
          displayName: sanitizedData.displayName,
          email: sanitizedData.email,
          password: data.password!,
          roles: sanitizedData.roles
        };
        await createUser(createData);
      }

      onSuccess();
    } catch (error) {
      console.error("Erro ao salvar usuário:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleDescription = (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    return role?.description || "";
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Informações básicas */}
      <Card>
        <CardHeader>
          <CardTitle>Informações Básicas</CardTitle>
          <CardDescription>
            Dados principais do usuário
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="displayName">Nome Completo</Label>
            <Input
              id="displayName"
              {...register("displayName", {
                required: "Nome é obrigatório",
                minLength: { value: 2, message: "Nome deve ter pelo menos 2 caracteres" }
              })}
              placeholder="Digite o nome completo"
            />
            {errors.displayName && (
              <p className="text-sm text-destructive">{errors.displayName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...register("email", {
                required: "Email é obrigatório",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Email inválido"
                }
              })}
              placeholder="Digite o email"
              disabled={isEditing} // Email não pode ser alterado
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          {!isEditing && (
            <>
              <div className="space-y-2">
                <Label htmlFor="password">Senha</Label>
                <Input
                  id="password"
                  type="password"
                  {...register("password", {
                    required: "Senha é obrigatória",
                    minLength: { value: 6, message: "Senha deve ter pelo menos 6 caracteres" }
                  })}
                  placeholder="Digite a senha"
                />
                {errors.password && (
                  <p className="text-sm text-destructive">{errors.password.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirmar Senha</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  {...register("confirmPassword", {
                    required: "Confirmação de senha é obrigatória",
                    validate: value => value === password || "As senhas não coincidem"
                  })}
                  placeholder="Confirme a senha"
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-destructive">{errors.confirmPassword.message}</p>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Roles e Permissões */}
      <Card>
        <CardHeader>
          <CardTitle>Roles e Permissões</CardTitle>
          <CardDescription>
            Selecione as roles que definem as permissões do usuário
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {roles.map((role) => (
              <div key={role.id} className="flex items-start space-x-3">
                <Checkbox
                  id={role.id}
                  checked={selectedRoles.includes(role.id)}
                  onCheckedChange={() => handleRoleToggle(role.id)}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor={role.id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {role.name}
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    {role.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {selectedRoles.length === 0 && (
            <p className="text-sm text-destructive mt-2">
              Selecione pelo menos uma role para o usuário.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Botões de ação */}
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit" disabled={isSubmitting || loading}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isEditing ? "Atualizar" : "Criar"} Usuário
        </Button>
      </div>
    </form>
  );
};

export default UserForm;

import { BookOpen, List } from "lucide-react";
import { ModuleConfig } from "@/types/module";
import CadastroPedidos from "./pages/CadastroPedidos";
import <PERSON>ulpito from "./pages/Pulpito";

export const prayerModule: ModuleConfig = {
  id: "prayer",
  name: "Pedidos e Avisos",
  description: "Gerenciamento de pedidos de oração e avisos",
  icon: BookOpen,
  routes: [
    {
      path: "/pedidos",
      component: <PERSON><PERSON><PERSON>,
      name: "Listar",
      description: "Visualizar pedidos",
      icon: List,
    },
    {
      path: "/pedidos/cadastro",
      component: CadastroPedidos,
      name: "Cadastrar",
      description: "Cadastrar pedidos de oração e avisos",
      icon: BookOpen,
    },
  ],
};

import { useAuth } from '@/contexts/AuthContext';
import { SystemPermissions, SystemRoles } from '@/types/user';

export const usePermissions = () => {
  const { user, permissions, hasPermission, hasRole } = useAuth();

  return {
    // Estado do usuário
    user,
    isAuthenticated: !!user,
    isActive: user?.isActive ?? false,

    // Verificações de role
    isAdmin: hasRole(SystemRoles.ADMIN),
    isModerator: hasRole(SystemRoles.MODERATOR),
    isUser: hasRole(SystemRoles.USER),
    isViewer: hasRole(SystemRoles.VIEWER),

    // Permissões gerais
    permissions,
    hasPermission,
    hasRole,

    // Permissões específicas para usuários
    canCreateUsers: permissions.canCreateUsers,
    canEditUsers: permissions.canEditUsers,
    canDeleteUsers: permissions.canDeleteUsers,
    canViewUsers: permissions.canViewUsers,
    canManageRoles: permissions.canManageRoles,

    // Permissões para pedidos de oração
    canCreatePrayers: hasPermission(SystemPermissions.PRAYERS_CREATE),
    canReadPrayers: hasPermission(SystemPermissions.PRAYERS_READ),
    canUpdatePrayers: hasPermission(SystemPermissions.PRAYERS_UPDATE),
    canDeletePrayers: hasPermission(SystemPermissions.PRAYERS_DELETE),
    canModeratePrayers: hasPermission(SystemPermissions.PRAYERS_MODERATE),

    // Permissões para eventos
    canCreateEvents: hasPermission(SystemPermissions.EVENTS_CREATE),
    canReadEvents: hasPermission(SystemPermissions.EVENTS_READ),
    canUpdateEvents: hasPermission(SystemPermissions.EVENTS_UPDATE),
    canDeleteEvents: hasPermission(SystemPermissions.EVENTS_DELETE),

    // Permissões para membros
    canCreateMembers: hasPermission(SystemPermissions.MEMBERS_CREATE),
    canReadMembers: hasPermission(SystemPermissions.MEMBERS_READ),
    canUpdateMembers: hasPermission(SystemPermissions.MEMBERS_UPDATE),
    canDeleteMembers: hasPermission(SystemPermissions.MEMBERS_DELETE),

    // Permissões para configurações
    canManageSettings: hasPermission(SystemPermissions.SETTINGS_MANAGE),

    // Permissões de sistema
    isSystemAdmin: hasPermission(SystemPermissions.SYSTEM_ADMIN),

    // Função para verificar múltiplas permissões
    hasAnyPermission: (permissionList: string[]) => {
      return permissionList.some(permission => hasPermission(permission));
    },

    // Função para verificar se tem todas as permissões
    hasAllPermissions: (permissionList: string[]) => {
      return permissionList.every(permission => hasPermission(permission));
    },

    // Função para verificar se pode acessar uma rota específica
    canAccessRoute: (routePath: string) => {
      // Definir regras de acesso por rota
      const routePermissions: Record<string, string[]> = {
        '/usuarios': [SystemPermissions.USERS_READ],
        '/usuarios/novo': [SystemPermissions.USERS_CREATE],
        '/usuarios/roles': [SystemPermissions.ROLES_MANAGE],

        '/pedidos': [SystemPermissions.PRAYERS_READ],
        '/pedidos/cadastro': [SystemPermissions.PRAYERS_CREATE],

        '/eventos': [SystemPermissions.EVENTS_READ],
        '/eventos/novo': [SystemPermissions.EVENTS_CREATE],

        '/membros': [SystemPermissions.MEMBERS_READ],
        '/membros/novo': [SystemPermissions.MEMBERS_CREATE],

        '/cargos': [SystemPermissions.POSITIONS_READ],
        '/cargos/novo': [SystemPermissions.POSITIONS_CREATE],

        '/congregacoes': [SystemPermissions.CONGREGATIONS_READ],
        '/congregacoes/novo': [SystemPermissions.CONGREGATIONS_CREATE],

        '/dashboard/settings': [SystemPermissions.SETTINGS_MANAGE]
      };

      const requiredPermissions = routePermissions[routePath];
      if (!requiredPermissions) return true; // Rota pública ou não definida

      return requiredPermissions.some(permission => hasPermission(permission));
    },
  };
};

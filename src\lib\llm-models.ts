import { LLMProvider } from "@/types/llm";

export interface LLMModel {
  id: string;
  name: string;
  provider: LLMProvider;
  isRecommended: boolean;
  isFree: boolean;
  description?: string;
}

// Modelos recomendados e gratuitos para cada provedor
const RECOMMENDED_MODELS: Record<LLMProvider, LLMModel[]> = {
  openai: [
    {
      id: "gpt-3.5-turbo",
      name: "GPT-3.5 Turbo",
      provider: "openai",
      isRecommended: true,
      isFree: false,
      description: "Bom equilíbrio entre custo e desempenho"
    },
    {
      id: "gpt-4o",
      name: "GPT-4o",
      provider: "openai",
      isRecommended: true,
      isFree: false,
      description: "<PERSON><PERSON> desempenho, mais caro"
    }
  ],
  openrouter: [
    {
      id: "openai/gpt-3.5-turbo",
      name: "GPT-3.5 Turbo (via OpenRouter)",
      provider: "openrouter",
      isRecommended: true,
      isFree: false,
      description: "Bom equilíbrio entre custo e desempenho"
    },
    {
      id: "anthropic/claude-3-haiku",
      name: "Claude 3 Haiku",
      provider: "openrouter",
      isRecommended: true,
      isFree: false,
      description: "Rápido e eficiente"
    },
    {
      id: "google/gemma-7b-it",
      name: "Gemma 7B",
      provider: "openrouter",
      isRecommended: false,
      isFree: true,
      description: "Modelo gratuito com bom desempenho"
    }
  ],
  groq: [
    {
      id: "llama3-8b-8192",
      name: "Llama 3 8B",
      provider: "groq",
      isRecommended: true,
      isFree: true,
      description: "Rápido e gratuito"
    },
    {
      id: "llama3-70b-8192",
      name: "Llama 3 70B",
      provider: "groq",
      isRecommended: true,
      isFree: true,
      description: "Melhor desempenho, gratuito"
    },
    {
      id: "mixtral-8x7b-32768",
      name: "Mixtral 8x7B",
      provider: "groq",
      isRecommended: false,
      isFree: true,
      description: "Bom para tarefas complexas"
    }
  ]
};

// Função para obter modelos da OpenAI
export const fetchOpenAIModels = async (apiKey: string): Promise<LLMModel[]> => {
  try {
    const response = await fetch("https://api.openai.com/v1/models", {
      headers: {
        "Authorization": `Bearer ${apiKey}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Erro ao obter modelos da OpenAI: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Filtrar apenas modelos GPT e adicionar os recomendados
    const gptModels = data.data
      .filter((model: any) => 
        model.id.includes("gpt") && 
        !model.id.includes("instruct") && 
        !model.id.includes("vision")
      )
      .map((model: any) => ({
        id: model.id,
        name: model.id,
        provider: "openai" as LLMProvider,
        isRecommended: false,
        isFree: false
      }));
    
    // Adicionar informações de recomendação
    return mergeWithRecommended(gptModels, "openai");
  } catch (error) {
    console.error("Erro ao buscar modelos da OpenAI:", error);
    // Retornar apenas os modelos recomendados em caso de erro
    return RECOMMENDED_MODELS.openai;
  }
};

// Função para obter modelos do OpenRouter
export const fetchOpenRouterModels = async (apiKey: string): Promise<LLMModel[]> => {
  try {
    const response = await fetch("https://openrouter.ai/api/v1/models", {
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "HTTP-Referer": window.location.origin,
        "X-Title": "Belém Connect"
      }
    });
    
    if (!response.ok) {
      throw new Error(`Erro ao obter modelos do OpenRouter: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Mapear modelos do OpenRouter
    const models = data.data.map((model: any) => ({
      id: model.id,
      name: model.name || model.id,
      provider: "openrouter" as LLMProvider,
      isRecommended: false,
      isFree: model.pricing?.prompt === 0 && model.pricing?.completion === 0
    }));
    
    // Adicionar informações de recomendação
    return mergeWithRecommended(models, "openrouter");
  } catch (error) {
    console.error("Erro ao buscar modelos do OpenRouter:", error);
    // Retornar apenas os modelos recomendados em caso de erro
    return RECOMMENDED_MODELS.openrouter;
  }
};

// Função para obter modelos do Groq
export const fetchGroqModels = async (apiKey: string): Promise<LLMModel[]> => {
  try {
    const response = await fetch("https://api.groq.com/openai/v1/models", {
      headers: {
        "Authorization": `Bearer ${apiKey}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Erro ao obter modelos do Groq: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Mapear modelos do Groq
    const models = data.data.map((model: any) => ({
      id: model.id,
      name: model.id,
      provider: "groq" as LLMProvider,
      isRecommended: false,
      isFree: true // Groq é gratuito durante o período beta
    }));
    
    // Adicionar informações de recomendação
    return mergeWithRecommended(models, "groq");
  } catch (error) {
    console.error("Erro ao buscar modelos do Groq:", error);
    // Retornar apenas os modelos recomendados em caso de erro
    return RECOMMENDED_MODELS.groq;
  }
};

// Função para mesclar modelos da API com os recomendados
const mergeWithRecommended = (apiModels: LLMModel[], provider: LLMProvider): LLMModel[] => {
  const recommendedIds = RECOMMENDED_MODELS[provider].map(model => model.id);
  
  // Remover modelos recomendados da lista da API para evitar duplicatas
  const filteredApiModels = apiModels.filter(model => !recommendedIds.includes(model.id));
  
  // Combinar modelos recomendados com os da API
  return [...RECOMMENDED_MODELS[provider], ...filteredApiModels];
};

// Função para obter modelos com base no provedor
export const fetchModelsByProvider = async (provider: LLMProvider, apiKey: string): Promise<LLMModel[]> => {
  if (!apiKey) {
    return RECOMMENDED_MODELS[provider];
  }
  
  switch (provider) {
    case "openai":
      return fetchOpenAIModels(apiKey);
    case "openrouter":
      return fetchOpenRouterModels(apiKey);
    case "groq":
      return fetchGroqModels(apiKey);
    default:
      return [];
  }
};

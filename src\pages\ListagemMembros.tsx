import { useQuery } from "@tanstack/react-query";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { User<PERSON><PERSON>, Printer } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface Membro {
  id: string;
  nome: string;
  dataNascimento: string;
  dataBatismo?: string;
  rg: string;
  cpf: string;
  endereco: string;
  telefone: string;
  email: string;
  congregacao: string;
}

const ListagemMembros = () => {
  const { data: membros, isLoading } = useQuery({
    queryKey: ["membros"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "membros"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Membro[];
    },
  });

  if (isLoading) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Membros</h1>
        <Link to="/cadastro-membros">
          <Button>
            <UserPlus className="mr-2 h-4 w-4" />
            Novo Membro
          </Button>
        </Link>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Congregação</TableHead>
              <TableHead>Telefone</TableHead>
              <TableHead>Data de Batismo</TableHead>
              <TableHead>Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {membros?.map((membro) => (
              <TableRow key={membro.id}>
                <TableCell className="font-medium">{membro.nome}</TableCell>
                <TableCell>{membro.congregacao}</TableCell>
                <TableCell>{membro.telefone}</TableCell>
                <TableCell>
                  {membro.dataBatismo
                    ? format(new Date(membro.dataBatismo), "dd/MM/yyyy", {
                        locale: ptBR,
                      })
                    : "Não batizado"}
                </TableCell>
                <TableCell>
                  <Link to={`/cartao-membro/${membro.id}`}>
                    <Button variant="ghost" size="sm">
                      <Printer className="h-4 w-4" />
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default ListagemMembros;
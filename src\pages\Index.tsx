import { useQuery } from "@tanstack/react-query";
import { collection, getDocs, query, where, orderBy, limit } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Users, BookOpen, Church, Calendar } from "lucide-react";
import { StatsCard } from "@/components/dashboard/StatsCard";
import { ActivityFeed } from "@/components/dashboard/ActivityFeed";
import { PrayerRequestsChart } from "@/components/dashboard/PrayerRequestsChart";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { initializeUserSystem } from "@/scripts/init-user-system";

interface PedidoOracao {
  origem: string;
  createdAt: string;
}

interface Membro {
  dataBatismo?: string;
  congregacao: string;
}

interface Event {
  id: string;
  title: string;
  date: string;
  time: string;
  congregationId: string;
}

interface PedidosOrigemCount {
  [key: string]: number;
}

const Index = () => {

  // Descomentar para inicializar sistema de usuários (apenas primeira execução)
  // initializeUserSystem()

  const { data: pedidos } = useQuery({
    queryKey: ["pedidos"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "pedidos"));
      return querySnapshot.docs.map((doc) => doc.data()) as PedidoOracao[];
    },
  });

  const { data: membros } = useQuery({
    queryKey: ["membros"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "membros"));
      return querySnapshot.docs.map((doc) => doc.data()) as Membro[];
    },
  });

  const { data: upcomingEvents } = useQuery({
    queryKey: ["upcoming-events"],
    queryFn: async () => {
      const today = new Date().toISOString().split('T')[0];
      const eventsRef = collection(db, "events");
      const q = query(
        eventsRef,
        where("date", ">=", today),
        orderBy("date", "asc"),
        limit(5)
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Event[];
    },
  });

  const totalMembros = membros?.length || 0;
  const membrosBatizados = membros?.filter((m) => m.dataBatismo)?.length || 0;
  const totalPedidos = pedidos?.length || 0;
  const totalEventos = upcomingEvents?.length || 0;

  const pedidosPorOrigem = pedidos?.reduce((acc: PedidosOrigemCount, pedido) => {
    acc[pedido.origem] = (acc[pedido.origem] || 0) + 1;
    return acc;
  }, {});

  const dadosGrafico = Object.entries(pedidosPorOrigem || {}).map(
    ([origem, quantidade]) => ({
      origem,
      quantidade: quantidade as number,
    })
  );

  const recentActivities = [
    ...(upcomingEvents?.map((event) => ({
      title: "Próximo Evento",
      description: event.title,
      timeAgo: `${format(new Date(event.date), "PPP", { locale: ptBR })} às ${event.time}`,
    })) || []),
    {
      title: "Novo membro cadastrado",
      description: "João Silva foi adicionado como membro",
      timeAgo: "Agora",
    },
    {
      title: "Pedido de oração registrado",
      description: "Novo pedido da Congregação Central",
      timeAgo: "2h atrás",
    },
  ].slice(0, 5);

  return (
    <div className="w-full space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Bem-vindo ao painel de controle da igreja
          </p>
        </div>
        <select className="px-4 py-2 rounded-lg bg-card text-card-foreground border border-border">
          <option>Última semana</option>
          <option>Último mês</option>
          <option>Último ano</option>
        </select>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 w-full">
        <StatsCard
          title="Total de Membros"
          value={totalMembros}
          icon={Users}
          prefix=""
          change={{
            value: 2.1,
            timeframe: "ao mês anterior",
            trend: "up",
          }}
        />
        <StatsCard
          title="Membros Batizados"
          value={membrosBatizados}
          icon={Church}
          prefix=""
          change={{
            value: ((membrosBatizados / totalMembros) * 100),
            timeframe: "do total",
            trend: "up",
          }}
        />
        <StatsCard
          title="Pedidos de Oração"
          value={totalPedidos}
          icon={BookOpen}
          prefix=""
          change={{
            value: 5.2,
            timeframe: "à semana anterior",
            trend: "down",
          }}
        />
        <StatsCard
          title="Próximos Eventos"
          value={totalEventos}
          icon={Calendar}
          prefix=""
          change={{
            value: 1,
            timeframe: "eventos esta semana",
            trend: "up",
          }}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 w-full">
        <PrayerRequestsChart data={dadosGrafico} />
        <ActivityFeed activities={recentActivities} />
      </div>
    </div>
  );
};

export default Index;
import { PrayerForm } from "@/components/prayer/PrayerForm";
import { TagInput } from "@/components/prayer/TagInput";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { PedidoStatus } from "@/types/prayer";
import { addDoc, collection, getDocs } from "firebase/firestore";
import { useEffect, useState } from "react";

const CadastroPedidos = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [suggestedTags, setSuggestedTags] = useState<string[]>([]);

  useEffect(() => {
    const fetchTags = async () => {
      try {
        const querySnapshot = await getDocs(collection(db, "pedidos-oracao"));
        const existingTags = new Set<string>();

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          if (data.tags && Array.isArray(data.tags)) {
            data.tags.forEach((tag: string) => existingTags.add(tag));
          }
        });

        setSuggestedTags(Array.from(existingTags).sort());
      } catch (error) {
        console.error("Erro ao buscar tags:", error);
      }
    };

    fetchTags();
  }, []);

  const handleAddTag = (tag: string) => {
    if (!tags.includes(tag)) {
      setTags([...tags, tag]);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const onSubmit = async (data: any) => {
    setLoading(true);
    try {
      await addDoc(collection(db, "pedidos-oracao"), {
        ...data,
        tags,
        status: "pendente" as PedidoStatus,
        lido: false, // Mantido para compatibilidade com código antigo
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      toast({
        title: "Cadastrado com sucesso!",
        description:
          data.tipo === "pedido"
            ? "O pedido de oração foi registrado."
            : "O aviso foi registrado.",
      });

      setTags([]);
    } catch (error) {
      console.error("Erro ao cadastrar:", error);
      toast({
        variant: "destructive",
        title: "Erro ao cadastrar",
        description: "Tente novamente.",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-8 animate-fade-in">
      <PrayerForm onSubmit={onSubmit} loading={loading}>
        <TagInput
          tags={tags}
          suggestedTags={suggestedTags}
          onAddTag={handleAddTag}
          onRemoveTag={handleRemoveTag}
        />
      </PrayerForm>
    </div>
  );
};

export default CadastroPedidos;

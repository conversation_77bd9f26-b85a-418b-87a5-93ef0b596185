@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 47% 11%;
    --foreground: 0 0% 100%;

    --card: 217 33% 17%;
    --card-foreground: 0 0% 100%;

    --popover: 222 47% 11%;
    --popover-foreground: 0 0% 100%;

    --primary: 162 47% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 0 0% 100%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217 33% 17%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 162 47% 50%;

    --radius: 0.75rem;

    --sidebar-background: 222 47% 11%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 162 47% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 217 33% 17%;
    --sidebar-ring: 162 47% 50%;

    /* Sidebar width variables */
    --sidebar-width: 16rem;
    --sidebar-width-icon: 3rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
  }
}

@layer components {
  /* Melhorias para responsividade do sidebar */
  .sidebar-mobile-overlay {
    @apply fixed inset-0 z-40 bg-black/50 md:hidden;
  }

  /* Garantir que o conteúdo principal ocupe toda a largura em mobile */
  @media (max-width: 767px) {
    .main-content {
      margin-left: 0 !important;
      width: 100% !important;
    }
  }
}

export type PedidoStatus =
  | "revisao"
  | "pendente"
  | "lido"
  | "descartado"
  | "depois";

export interface PedidoOracao {
  id: string;
  titulo: string;
  descricao: string;
  nome: string;
  pedido: string;
  tipo: "pedido" | "aviso";
  origem: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  status: PedidoStatus;
  lido?: boolean; // Mantido para compatibilidade com dados existentes
}

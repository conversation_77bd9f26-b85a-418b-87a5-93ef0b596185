import { Users, Badge, Church } from "lucide-react";
import ListagemMembros from "./pages/ListagemMembros";
import GerenciamentoCargos from "./pages/GerenciamentoCargos";
import GerenciamentoCongregacoes from "./pages/GerenciamentoCongregacoes";
import { ModuleConfig } from "@/types/module";

export const memberModule: ModuleConfig = {
  id: "member",
  name: "Membros",
  description: "Gerenciamento de membros e cargos da igreja",
  icon: Users,
  routes: [
    {
      name: "Membros",
      path: "/membros",
      icon: Users,
      component: ListagemMembros,
      description: "Listagem e cadastro de membros",
    },
    {
      name: "Cargos",
      path: "/cargos",
      icon: Badge,
      component: GerenciamentoCargos,
      description: "Gerenciamento de cargos e funções",
    },
    {
      name: "Congregações",
      path: "/congregacoes",
      icon: Church,
      component: GerenciamentoCongregacoes,
      description: "Gerenciamento de congregações",
    },
  ],
};
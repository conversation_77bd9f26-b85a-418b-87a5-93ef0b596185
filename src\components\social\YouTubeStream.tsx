import { useQuery } from "@tanstack/react-query";
import YouTube from "react-youtube";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  collection,
  getDocs,
  doc,
  getDoc,
  query,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Congregation, CongregationService } from "@/types/congregation";
import {
  format,
  formatDistanceToNow,
  setHours,
  setMinutes,
  setSeconds,
} from "date-fns";
import { ptBR } from "date-fns/locale";
import { useEffect, useState, useRef } from "react";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { YouTubeLiveStream } from "@/types/youtube";
import {
  updateLiveStreamStatus,
  fetchLiveStreamComments,
  processComments,
} from "@/lib/youtube";
import { getDayOfWeek } from "@/lib/utils";

interface YouTubeStreamProps {
  videoId?: string;
  isLive?: boolean;
  autoCheck?: boolean;
  respectPlayerState?: boolean; // Se true, só atualiza o status quando o player terminar
}

export const YouTubeStream = ({
  videoId: propVideoId,
  isLive: propIsLive,
  autoCheck = false,
  respectPlayerState = false,
}: YouTubeStreamProps) => {
  const [currentVideoId, setCurrentVideoId] = useState<string | undefined>(
    propVideoId
  );
  const [isCurrentlyLive, setIsCurrentlyLive] = useState<boolean>(
    propIsLive || false
  );
  const [streamTitle, setStreamTitle] = useState<string>("");
  const [playerEnded, setPlayerEnded] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const playerRef = useRef<any>(null);
  const { toast } = useToast();

  // Buscar configurações e status atual do YouTube
  const { data: youtubeSettings } = useQuery({
    queryKey: ["youtube-settings"],
    queryFn: async () => {
      const docRef = doc(db, "system", "settings");
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        return docSnap.data();
      }
      return {};
    },
    refetchInterval: autoCheck ? 60000 : false, // Verificar a cada minuto
    enabled: autoCheck,
  });

  // Buscar transmissão ao vivo atual se existir
  const { data: currentLiveStream } = useQuery({
    queryKey: [
      "current-live-stream",
      youtubeSettings?.youtubeMonitoring?.currentLiveStreamId,
    ],
    queryFn: async () => {
      if (!youtubeSettings?.youtubeMonitoring?.currentLiveStreamId) {
        return null;
      }

      const docRef = doc(
        db,
        "youtube-streams",
        youtubeSettings.youtubeMonitoring.currentLiveStreamId
      );
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return docSnap.data() as YouTubeLiveStream;
      }

      return null;
    },
    enabled: !!youtubeSettings?.youtubeMonitoring?.currentLiveStreamId,
  });

  // Buscar última transmissão encerrada
  const { data: lastStream } = useQuery({
    queryKey: ["last-stream"],
    queryFn: async () => {
      // Buscar apenas se não houver transmissão ao vivo
      if (youtubeSettings?.youtubeMonitoring?.currentLiveStreamId) {
        return null;
      }

      try {
        // Buscar a última transmissão encerrada
        const q = query(
          collection(db, "youtube-streams"),
          where("isLive", "==", false),
          orderBy("endTime", "desc"),
          limit(1)
        );

        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
          return null;
        }

        return querySnapshot.docs[0].data() as YouTubeLiveStream;
      } catch (error) {
        console.error("Erro ao buscar última transmissão:", error);
        return null;
      }
    },
    enabled:
      autoCheck && !youtubeSettings?.youtubeMonitoring?.currentLiveStreamId,
    refetchInterval: autoCheck ? 300000 : false, // Verificar a cada 5 minutos
  });

  // Atualizar estado com base nas props ou dados do Firestore
  useEffect(() => {
    if (propVideoId) {
      // Se receber props diretamente, usar elas
      setCurrentVideoId(propVideoId);
      setIsCurrentlyLive(propIsLive || false);
    } else if (currentLiveStream?.isLive) {
      // Se houver uma transmissão ao vivo
      setCurrentVideoId(currentLiveStream.videoId);
      setIsCurrentlyLive(true);
      setStreamTitle(currentLiveStream.title);
    } else if (lastStream && !currentLiveStream) {
      // Se não houver transmissão ao vivo, mas houver uma última transmissão
      setCurrentVideoId(lastStream.videoId);
      setIsCurrentlyLive(false);
      setStreamTitle(`Última transmissão: ${lastStream.title}`);
    } else {
      // Nenhuma transmissão encontrada
      setCurrentVideoId(undefined);
      setIsCurrentlyLive(false);
      setStreamTitle("");
    }
  }, [propVideoId, propIsLive, currentLiveStream, lastStream]);

  const { data: congregations } = useQuery({
    queryKey: ["congregations"],
    queryFn: async () => {
      const querySnapshot = await getDocs(collection(db, "congregacoes"));
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Congregation[];
    },
  });

  const findNextService = () => {
    if (!congregations) return null;

    const now = new Date();
    let nextService: {
      service: CongregationService;
      congregation: Congregation;
    } | null = null;
    let earliestDiff = Infinity;

    congregations.forEach((congregation) => {
      congregation.services?.forEach((service) => {
        const [hours, minutes] = service.time.split(":").map(Number);
        const serviceDate = setSeconds(
          setMinutes(setHours(new Date(), hours), minutes),
          0
        );

        // Ajusta para o próximo dia da semana se necessário
        while (serviceDate.getDay() !== service.dayOfWeek) {
          serviceDate.setDate(serviceDate.getDate() + 1);
        }

        const diff = serviceDate.getTime() - now.getTime();
        if (diff > 0 && diff < earliestDiff) {
          earliestDiff = diff;
          nextService = { service, congregation };
        }
      });
    });

    return nextService;
  };

  const nextService = findNextService();

  const opts = {
    height: "100%",
    width: "100%",
    playerVars: {
      autoplay: 0,
    },
  };

  // Manipuladores de eventos do player
  const onPlayerReady = (event: any) => {
    playerRef.current = event.target;
  };

  const onPlayerStateChange = (event: any) => {
    // Estado 0 significa que o vídeo terminou
    if (event.data === 0) {
      setPlayerEnded(true);

      // Se estiver configurado para respeitar o estado do player e o vídeo terminou
      if (respectPlayerState && currentLiveStream?.id && isCurrentlyLive) {
        console.log("Vídeo terminou, atualizando status da transmissão");
        updateLiveStreamStatus(currentLiveStream.id, false);
        setIsCurrentlyLive(false);
      }
    }
  };

  // Função para processar comentários manualmente
  const handleProcessComments = async () => {
    if (!currentVideoId || isProcessing) return;

    setIsProcessing(true);

    try {
      console.log(
        `Processando comentários manualmente para o vídeo: ${currentVideoId}`
      );

      // Buscar comentários
      const comments = await fetchLiveStreamComments(currentVideoId);

      if (comments.length === 0) {
        toast({
          title: "Nenhum comentário encontrado",
          description: "Não foram encontrados comentários para processar.",
        });
        return;
      }

      // Processar comentários
      await processComments(comments);

      toast({
        title: "Comentários processados",
        description: `${comments.length} comentários foram processados com sucesso.`,
      });
    } catch (error) {
      console.error("Erro ao processar comentários:", error);
      toast({
        variant: "destructive",
        title: "Erro ao processar comentários",
        description:
          "Ocorreu um erro ao processar os comentários. Verifique o console para mais detalhes.",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Formatar a data da última transmissão
  const formatLastStreamDate = (dateString?: string) => {
    if (!dateString) return "";

    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: ptBR,
      });
    } catch (error) {
      return "";
    }
  };

  return (
    <Card className="bg-[#1A1F2C] border-0">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-medium">
              {isCurrentlyLive
                ? "Transmissão ao Vivo"
                : lastStream
                ? "Última Transmissão"
                : "Próximo Culto"}
            </CardTitle>
            {isCurrentlyLive && streamTitle && (
              <CardDescription className="text-white/70">
                {streamTitle}
                <Badge className="ml-2 bg-red-500">AO VIVO</Badge>
              </CardDescription>
            )}
            {!isCurrentlyLive && lastStream && (
              <CardDescription className="text-white/70">
                {lastStream.title}
                <Badge className="ml-2 bg-gray-500">
                  Encerrada {formatLastStreamDate(lastStream.endTime)}
                </Badge>
              </CardDescription>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {currentVideoId ? (
          <YouTube
            videoId={currentVideoId}
            opts={opts}
            className="w-full aspect-video"
            onReady={onPlayerReady}
            onStateChange={onPlayerStateChange}
          />
        ) : nextService ? (
          <div className="text-center py-12 space-y-4">
            <p className="text-2xl font-semibold">
              Próximo culto{" "}
              {nextService.service.type === "culto"
                ? ""
                : `(${nextService.service.type})`}
            </p>
            <p className="text-xl">
              {getDayOfWeek(nextService.service.dayOfWeek)}
              {format(
                setHours(
                  setMinutes(
                    new Date(),
                    parseInt(nextService.service.time.split(":")[1])
                  ),
                  parseInt(nextService.service.time.split(":")[0])
                ),
                " 'às' HH:mm",
                { locale: ptBR }
              )}
            </p>
            <p className="text-muted-foreground">
              {nextService.congregation.nome}
            </p>
            <p className="text-sm text-muted-foreground">
              {nextService.service.description}
            </p>
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              Nenhum culto programado no momento
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

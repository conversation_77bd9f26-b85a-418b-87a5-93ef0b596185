import { User, SystemRoles } from '@/types/user';

// Função para sanitizar entrada de texto
export const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove caracteres HTML básicos
    .substring(0, 500); // <PERSON><PERSON> ta<PERSON>
};

// Função para validar email
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  return emailRegex.test(email);
};

// Função para validar senha forte
export const isStrongPassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Senha deve ter pelo menos 8 caracteres');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('<PERSON>ha deve conter pelo menos uma letra maiúscula');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra minúscula');
  }

  if (!/[0-9]/.test(password)) {
    errors.push('Senha deve conter pelo menos um número');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Senha deve conter pelo menos um caractere especial');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Função para validar se usuário pode executar ação em outro usuário
export const canUserModifyUser = (
  currentUser: User,
  targetUser: User,
  action: 'update' | 'delete' | 'create'
): { canModify: boolean; reason?: string } => {
  // Admin pode fazer tudo
  if (currentUser.roles.includes(SystemRoles.ADMIN)) {
    return { canModify: true };
  }

  // Usuário não pode modificar a si mesmo (exceto alguns campos)
  if (currentUser.id === targetUser.id && action !== 'update') {
    return {
      canModify: false,
      reason: 'Usuário não pode executar esta ação em si mesmo'
    };
  }

  // Usuário não pode modificar admin
  if (targetUser.roles.includes(SystemRoles.ADMIN)) {
    return {
      canModify: false,
      reason: 'Não é possível modificar usuários administradores'
    };
  }

  // Moderador não pode modificar outros moderadores
  if (currentUser.roles.includes(SystemRoles.MODERATOR) &&
    targetUser.roles.includes(SystemRoles.MODERATOR) &&
    currentUser.id !== targetUser.id) {
    return {
      canModify: false,
      reason: 'Moderadores não podem modificar outros moderadores'
    };
  }

  return { canModify: true };
};

// Função para validar dados de usuário
export const validateUserData = (data: {
  displayName?: string;
  email?: string;
  roles?: string[];
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (data.displayName !== undefined) {
    if (!data.displayName || data.displayName.trim().length < 2) {
      errors.push('Nome deve ter pelo menos 2 caracteres');
    }

    if (data.displayName.length > 100) {
      errors.push('Nome não pode ter mais de 100 caracteres');
    }
  }

  if (data.email !== undefined) {
    if (!data.email || !isValidEmail(data.email)) {
      errors.push('Email inválido');
    }
  }

  if (data.roles !== undefined) {
    if (!Array.isArray(data.roles) || data.roles.length === 0) {
      errors.push('Usuário deve ter pelo menos uma role');
    }

    // Verificar se todas as roles são válidas
    // const validRoles = Object.values(SystemRoles);
    // const invalidRoles = data.roles.filter(role => !validRoles.includes(role as SystemRoles));

    // if (invalidRoles.length > 0) {
    //   errors.push(`Roles inválidas: ${invalidRoles.join(', ')}`);
    // }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Função para log de auditoria (placeholder - implementar conforme necessário)
export const logSecurityEvent = (
  userId: string,
  action: string,
  details: Record<string, any>,
  success: boolean
): void => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    userId,
    action,
    details,
    success,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server'
  };

  // Em produção, enviar para serviço de auditoria
  console.log('Security Event:', logEntry);
};

// Função para verificar rate limiting (placeholder)
export const checkRateLimit = (
  userId: string,
  action: string,
  windowMs: number = 60000, // 1 minuto
  maxAttempts: number = 5
): boolean => {
  // Em produção, implementar com Redis ou similar
  // Por enquanto, sempre permite
  return true;
};

// Função para detectar tentativas suspeitas
export const detectSuspiciousActivity = (
  userId: string,
  action: string,
  metadata: Record<string, any>
): boolean => {
  // Implementar lógica de detecção de atividade suspeita
  // Por exemplo: muitas tentativas de login, alterações em massa, etc.

  // Por enquanto, sempre retorna false (não suspeito)
  return false;
};

// Constantes de segurança
export const SECURITY_CONSTANTS = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION_MS: 15 * 60 * 1000, // 15 minutos
  PASSWORD_MIN_LENGTH: 8,
  SESSION_TIMEOUT_MS: 24 * 60 * 60 * 1000, // 24 horas
  MAX_USERS_PER_BATCH: 10,
  MAX_ROLE_ASSIGNMENTS: 5
} as const;

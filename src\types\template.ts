export type TemplateType = 'prayer' | 'announcement';

export interface Template {
  id: string;
  name: string;
  type: TemplateType;
  template: string;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  placeholders: string[];
}

export interface TemplateSettings {
  defaultPrayerTemplate: string;
  defaultAnnouncementTemplate: string;
}

export interface ProcessedTemplate {
  content: string;
  type: TemplateType;
  tags: string[];
  status: 'revisar' | 'aprovado' | 'rejeitado';
  origem: string;
}
import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "./ui/button";

export const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  const toogleTheme = () => theme === "light" ? setTheme("dark") : setTheme("light");
  return (
    <Button variant="ghost" size="icon" className="hover:bg-sidebar-accent" onClick={toogleTheme}>
      {theme === "light" ? (
        <Sun className="h-5 w-5" />
      ) : (
        <Moon className="h-5 w-5" />
      )}
    </Button>
  )

}
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface PrayerFormProps {
  onSubmit: (data: FormData) => Promise<void>;
  loading: boolean;
  children: React.ReactNode;
}

interface FormData {
  nome: string;
  pedido: string;
  tipo: "pedido" | "aviso";
}

export const PrayerForm = ({ onSubmit, loading, children }: PrayerFormProps) => {
  const { register, handleSubmit, setValue, watch } = useForm<FormData>({
    defaultValues: {
      tipo: "pedido",
    },
  });

  return (
    <div>
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Novo {watch("tipo") === "pedido" ? "Pedido de Oração" : "Aviso"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="tipo" className="text-sm font-medium">
              Tipo
            </label>
            <Select
              onValueChange={(value) => setValue("tipo", value as "pedido" | "aviso")}
              defaultValue="pedido"
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pedido">Pedido de Oração</SelectItem>
                <SelectItem value="aviso">Aviso</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="nome" className="text-sm font-medium">
              Nome
            </label>
            <Input
              id="nome"
              {...register("nome", { required: true })}
              placeholder="Digite o nome"
              className="w-full"
            />
          </div>
          
          {children}

          <div className="space-y-2">
            <label htmlFor="pedido" className="text-sm font-medium">
              {watch("tipo") === "pedido" ? "Pedido" : "Aviso"}
            </label>
            <Textarea
              id="pedido"
              {...register("pedido", { required: true })}
              placeholder={`Digite o ${watch("tipo") === "pedido" ? "pedido de oração" : "aviso"}`}
              className="min-h-[150px] w-full"
            />
          </div>

          <Button 
            type="submit" 
            className="w-full transition-all hover:scale-[1.02]" 
            disabled={loading}
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <span className="animate-pulse">Cadastrando...</span>
              </span>
            ) : (
              `Cadastrar ${watch("tipo") === "pedido" ? "Pedido" : "Aviso"}`
            )}
          </Button>
        </form>
      </CardContent>
    </div>
  );
};
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Maximize2 } from "lucide-react";
import { useEffect, useState } from "react";

interface PulpitoFullscreenToggleProps {
  setIsFullscreen: (isFullscreen: boolean) => void;
  isFullscreen: boolean;
}

export const PulpitoFullscreenToggle = ({ setIsFullscreen, isFullscreen }: PulpitoFullscreenToggleProps) => {
  const [exitHoldStart, setExitHoldStart] = useState<number | null>(null);
  const { toast } = useToast();
  const HOLD_DURATION = 2000; // 2 segundos para sair do modo fullscreen

  const toggleFullscreen = () => {
    const newState = !isFullscreen;
    setIsFullscreen(newState);

    if (newState) {
      toast({
        title: "Modo tela cheia ativado",
        description: "Segure o botão de sair por 2 segundos para desativar",
      });
    }
  };

  const handleExitMouseDown = () => {
    if (isFullscreen) {
      setExitHoldStart(Date.now());
    }
  };

  const handleExitMouseUp = () => {
    if (isFullscreen && exitHoldStart) {
      const holdDuration = Date.now() - exitHoldStart;
      if (holdDuration >= HOLD_DURATION) {
        toggleFullscreen();
      } else {
        toast({
          title: "Mantenha pressionado",
          description: "Segure o botão por 2 segundos para sair do modo tela cheia",
        });
      }
    }
    setExitHoldStart(null);
  };

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isFullscreen) {
        e.preventDefault();
        toast({
          title: "Use o botão para sair",
          description: "Segure o botão por 2 segundos para sair do modo tela cheia",
        });
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [isFullscreen, toast]);

  return isFullscreen ? (
    <Button
      variant="outline"
      size="icon"
      className="bg-background/50 backdrop-blur-sm"
      onClick={toggleFullscreen}
    // onMouseDown={handleExitMouseDown}
    // onMouseUp={handleExitMouseUp}
    // onMouseLeave={() => setExitHoldStart(null)}
    >
      <ArrowLeft className="h-4 w-4" />
    </Button>
  ) : (
    <Button
      variant="outline"
      size="icon"
      className="bg-background/50 backdrop-blur-sm"
      onClick={toggleFullscreen}
    >
      <Maximize2 className="h-4 w-4" />
    </Button>
  );
};
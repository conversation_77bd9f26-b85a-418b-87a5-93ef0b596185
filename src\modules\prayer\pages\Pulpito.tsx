import { PrayerCard } from "@/components/prayer/PrayerCard";
import { PrayerList } from "@/components/prayer/PrayerList";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { db } from "@/lib/firebase";
import { cn } from "@/lib/utils";
import { PedidoOracao, PedidoStatus } from "@/types/prayer";
import {
  collection,
  doc,
  onSnapshot,
  query,
  updateDoc,
  where,
} from "firebase/firestore";
import { useEffect, useState } from "react";

const Pulpito = () => {
  const { toast } = useToast();
  const [pedidos, setPedidos] = useState<PedidoOracao[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<"all" | "avisos" | "pedidos">("pedidos");
  const [statusFilter, setStatusFilter] = useState<PedidoStatus | "all">(
    "pendente"
  );
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [days, setDays] = useState(7);

  useEffect(() => {
    let q;

    // Construir a query com base nos filtros
    if (statusFilter === "all") {
      q = query(
        collection(db, "pedidos-oracao"),
        where(
          "createdAt",
          ">=",
          new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
        )
      );
    } else {
      q = query(
        collection(db, "pedidos-oracao"),
        where("status", "==", statusFilter),
        where(
          "createdAt",
          ">=",
          new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
        )
      );
    }

    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const pedidosData: PedidoOracao[] = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          // Compatibilidade com documentos antigos que usam lido em vez de status
          if (data.status === undefined && data.lido !== undefined) {
            data.status = data.lido ? "lido" : "pendente";
          }
          pedidosData.push({ id: doc.id, ...data } as PedidoOracao);
        });
        setPedidos(pedidosData);
        setLoading(false);
      },
      (error) => {
        console.error("Erro ao observar pedidos:", error);
        toast({
          variant: "destructive",
          title: "Erro ao carregar pedidos",
          description:
            "Não foi possível carregar os pedidos de oração em tempo real.",
        });
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [toast, days, statusFilter]);

  const atualizarStatus = async (
    pedidoId: string,
    novoStatus: PedidoStatus
  ) => {
    try {
      await updateDoc(doc(db, "pedidos-oracao", pedidoId), {
        status: novoStatus,
        lido: novoStatus === "lido", // Manter compatibilidade com código antigo
        updatedAt: new Date().toISOString(),
      });

      const mensagens = {
        revisao: {
          title: "Pedido marcado para revisão",
          description: "Este pedido será revisado posteriormente.",
        },
        pendente: {
          title: "Pedido marcado como pendente",
          description: "Este pedido voltou para a lista de pendentes.",
        },
        lido: {
          title: "Pedido marcado como lido",
          description: "Este pedido foi marcado como lido.",
        },
        descartado: {
          title: "Pedido descartado",
          description:
            "Este pedido foi descartado e não será mais exibido na lista principal.",
        },
        depois: {
          title: "Pedido marcado para depois",
          description: "Este pedido será tratado posteriormente.",
        },
      };

      toast({
        title: mensagens[novoStatus].title,
        description: mensagens[novoStatus].description,
      });
    } catch (error) {
      console.error(`Erro ao atualizar status para ${novoStatus}:`, error);
      toast({
        variant: "destructive",
        title: `Erro ao atualizar status`,
        description: `Não foi possível atualizar o status do pedido.`,
      });
    }
  };

  const marcarComoLido = async (pedidoId: string) => {
    await atualizarStatus(pedidoId, "lido");
  };

  const descartar = async (pedidoId: string) => {
    await atualizarStatus(pedidoId, "descartado");
  };

  const marcarParaDepois = async (pedidoId: string) => {
    await atualizarStatus(pedidoId, "depois");
  };

  const marcarParaRevisao = async (pedidoId: string) => {
    await atualizarStatus(pedidoId, "revisao");
  };

  const marcarComoPendente = async (pedidoId: string) => {
    await atualizarStatus(pedidoId, "pendente");
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-2xl animate-pulse">Carregando pedidos...</div>
      </div>
    );
  }

  const filteredPedidos = pedidos.filter((pedido) => {
    // Primeiro filtra por tipo (pedido/aviso)
    const tipoMatch =
      filter === "all"
        ? true
        : filter === "avisos"
        ? pedido.tipo === "aviso"
        : pedido.tipo === "pedido" || !pedido.tipo;

    // Depois filtra por status
    const statusMatch =
      statusFilter === "all" ? true : pedido.status === statusFilter;

    return tipoMatch && statusMatch;
  });

  const emptyMessage = () => {
    const tipoMsg =
      filter === "avisos"
        ? "aviso"
        : filter === "pedidos"
        ? "pedido de oração"
        : "pedido ou aviso";

    const statusMsg =
      statusFilter === "pendente"
        ? "pendente"
        : statusFilter === "revisao"
        ? "em revisão"
        : statusFilter === "depois"
        ? "marcado para depois"
        : statusFilter === "lido"
        ? "lido"
        : statusFilter === "descartado"
        ? "descartado"
        : "";

    return {
      title: `Nenhum ${tipoMsg} ${statusMsg}.`,
      description:
        statusFilter === "all"
          ? `Não há ${tipoMsg}s no sistema.`
          : `Não há ${tipoMsg}s com status "${statusMsg}".`,
    };
  };

  const toggleFilter = (filter: "all" | "avisos" | "pedidos") => {
    setFilter(filter);
    setCurrentIndex(0);
  };

  const toggleStatusFilter = (status: PedidoStatus | "all") => {
    setStatusFilter(status);
    setCurrentIndex(0);
  };

  const toogleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div
      className={cn(
        "relative transition-all duration-300",
        isFullscreen && "fixed inset-0 bg-background z-50 p-6"
      )}
    >
      <div className="space-y-8 animate-fade-in">
        <div className="flex justify-between items-center space-y-4">
          <h1 className="text-3xl font-bold">Pedidos e Avisos</h1>
          <div className="space-x-2">
            <Button
              variant="outline"
              onClick={() => toogleFullscreen()}
              className="transition-all hover:scale-105"
            >
              {isFullscreen ? "Sair do modo tela cheia" : "Modo tela cheia"}
            </Button>
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="space-x-2">
            <Button
              variant={days === 1 ? "default" : "outline"}
              onClick={() => setDays(1)}
              className="transition-all hover:scale-105"
            >
              Hoje
            </Button>
            <Button
              variant={days === 7 ? "default" : "outline"}
              onClick={() => setDays(7)}
              className="transition-all hover:scale-105"
            >
              Últimos 7 dias
            </Button>
            <Button
              variant={days === 30 ? "default" : "outline"}
              onClick={() => setDays(30)}
              className="transition-all hover:scale-105"
            >
              Últimos 30 dias
            </Button>
          </div>
          <div className="space-x-2">
            <Button
              variant={filter === "pedidos" ? "default" : "outline"}
              onClick={() => toggleFilter("pedidos")}
              className="transition-all hover:scale-105"
            >
              Pedidos de Oração
            </Button>
            <Button
              variant={filter === "avisos" ? "default" : "outline"}
              onClick={() => toggleFilter("avisos")}
              className="transition-all hover:scale-105"
            >
              Avisos
            </Button>
            <Button
              variant={filter === "all" ? "default" : "outline"}
              onClick={() => toggleFilter("all")}
              className="transition-all hover:scale-105"
            >
              Todos
            </Button>
          </div>
        </div>

        {/* Filtros de status */}
        <div className="flex justify-center items-center">
          <div className="space-x-2 flex flex-wrap gap-2 justify-center">
            <Button
              variant={statusFilter === "pendente" ? "default" : "outline"}
              onClick={() => toggleStatusFilter("pendente")}
              className="transition-all hover:scale-105"
            >
              Pendentes
            </Button>
            <Button
              variant={statusFilter === "revisao" ? "default" : "outline"}
              onClick={() => toggleStatusFilter("revisao")}
              className="transition-all hover:scale-105"
            >
              Em Revisão
            </Button>
            <Button
              variant={statusFilter === "depois" ? "default" : "outline"}
              onClick={() => toggleStatusFilter("depois")}
              className="transition-all hover:scale-105"
            >
              Para Depois
            </Button>
            <Button
              variant={statusFilter === "lido" ? "default" : "outline"}
              onClick={() => toggleStatusFilter("lido")}
              className="transition-all hover:scale-105"
            >
              Lidos
            </Button>
            <Button
              variant={statusFilter === "descartado" ? "default" : "outline"}
              onClick={() => toggleStatusFilter("descartado")}
              className="transition-all hover:scale-105"
            >
              Descartados
            </Button>
            <Button
              variant={statusFilter === "all" ? "default" : "outline"}
              onClick={() => toggleStatusFilter("all")}
              className="transition-all hover:scale-105"
            >
              Todos os Status
            </Button>
          </div>
        </div>

        {filteredPedidos.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[60vh] animate-fade-in">
            <h2 className="text-2xl font-semibold mb-4">
              {emptyMessage().title}
            </h2>
            <p className="text-gray-600">{emptyMessage().description}</p>
          </div>
        ) : isFullscreen ? (
          <PrayerCard
            pedido={filteredPedidos[currentIndex]}
            currentIndex={currentIndex}
            totalPedidos={filteredPedidos.length}
            onPrevious={() => setCurrentIndex(currentIndex - 1)}
            onNext={() => setCurrentIndex(currentIndex + 1)}
            onMarkAsRead={marcarComoLido}
            onDiscard={descartar}
            onMarkForLater={marcarParaDepois}
            onMarkForReview={marcarParaRevisao}
            onMarkAsPending={marcarComoPendente}
          />
        ) : (
          <PrayerList
            pedidos={filteredPedidos}
            onMarkAsRead={marcarComoLido}
            onDiscard={descartar}
            onMarkForLater={marcarParaDepois}
            onMarkForReview={marcarParaRevisao}
            onMarkAsPending={marcarComoPendente}
          />
        )}
      </div>
    </div>
  );
};

export default Pulpito;

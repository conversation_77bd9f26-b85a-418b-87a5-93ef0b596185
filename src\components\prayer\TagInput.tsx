import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface TagInputProps {
  tags: string[];
  suggestedTags: string[];
  onAddTag: (tag: string) => void;
  onRemoveTag: (tag: string) => void;
}

export const TagInput = ({ tags, suggestedTags, onAddTag, onRemoveTag }: TagInputProps) => {
  const [newTag, setNewTag] = useState("");
  const [openTagsPopover, setOpenTagsPopover] = useState(false);

  const handleAddTag = (tagToAdd: string) => {
    if (tagToAdd.trim()) {
      onAddTag(tagToAdd.trim().toLowerCase());
      setNewTag("");
      setOpenTagsPopover(false);
    }
  };

  return (
    <div className="space-y-2">
      <label htmlFor="tags" className="text-sm font-medium">
        Tags
      </label>
      <div className="flex gap-2">
        <Popover open={openTagsPopover} onOpenChange={setOpenTagsPopover}>
          <PopoverTrigger asChild>
            <Input
              id="tags"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleAddTag(newTag);
                }
              }}
              placeholder="Digite ou selecione uma tag"
              className="w-full"
            />
          </PopoverTrigger>
          <PopoverContent className="p-0" align="start">
            <Command>
              <CommandInput placeholder="Procurar tag..." />
              <CommandList>
                <CommandEmpty>Nenhuma tag encontrada.</CommandEmpty>
                <CommandGroup>
                  {suggestedTags
                    .filter((tag) => !tags.includes(tag))
                    .map((tag) => (
                      <CommandItem
                        key={tag}
                        onSelect={() => handleAddTag(tag)}
                      >
                        {tag}
                      </CommandItem>
                    ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <Button 
          type="button" 
          onClick={() => handleAddTag(newTag)}
          variant="secondary"
        >
          Adicionar
        </Button>
      </div>
      <div className="flex flex-wrap gap-2 mt-2">
        {tags.map((tag) => (
          <Badge key={tag} variant="secondary" className="flex items-center gap-1 animate-fade-in">
            {tag}
            <X
              className="h-3 w-3 cursor-pointer hover:text-destructive transition-colors"
              onClick={() => onRemoveTag(tag)}
            />
          </Badge>
        ))}
      </div>
    </div>
  );
};